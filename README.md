# Inteli - Instituto de Tecnologia e Liderança

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="assets/images/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# InfoPepper

## Chillibinos

## :student: Integrantes:

- <PERSON> - [ [Github](https://github.com/GabriellReisss) | [LinkedIn](https://www.linkedin.com/in/gabriel-reis-07170727b/) ]

- <PERSON> - [ [Github](https://github.com/Zanette00) | [LinkedIn](https://www.linkedin.com/in/gabriel-c-zanette/) ]

- <PERSON> - [ [Github](https://github.com/JoaoAidar) | [LinkedIn](https://www.linkedin.com/in/jo%C3%A3o-aidar-689097246/) ]

- <PERSON> - [ [Github](https://github.com/le<PERSON>) | [LinkedIn](https://www.linkedin.com/in/leonardoramos<PERSON>ira/) ]

- <PERSON> Josué - [ [Github](https://github.com/J05UE-l) | [LinkedIn](https://www.linkedin.com/in/rafael-josue/) ]

- Samuel Vono Godoi Chiovato - [ [Github](https://github.com/V0no) | [LinkedIn](https://www.linkedin.com/in/samuel-vono) ]

- Yan Dimitri Kruziski - [ [Github](https://github.com/yankruziski) | [LinkedIn](https://www.linkedin.com/in/yan-dimitri-kruziski/) ]

## :teacher: Professores:

### Orientador(a)

- <a href="https://www.linkedin.com/in/laizaribeiro/">Laíza Ribeiro Silva</a>

### Instrutores

- <a href="https://www.linkedin.com/in/cristiano-benites-ph-d-687647a8/">Cristiano da Silva Benites</a>
- <a href="https://www.linkedin.com/in/pedroteberga/">Pedro Marins Freire Teberga</a>
- <a href="https://www.linkedin.com/in/bruna-mayer/">Bruna Mayer Costa</a>
- <a href="https://www.linkedin.com/in/geraldo-magela-severino-vasconcelos-22b1b220/">Geraldo Magela Severino Vasconcelos</a>
- <a href="https://www.linkedin.com/in/marcelo-gon%C3%A7alves-phd-a550652/">Marcelo Luizdo Amaral Gonçalves</a>

## 📝 Descrição

Este projeto tem como objetivo construir um pipeline reprodutível de preparação, análise exploratória, modelagem de baselines e modelo de potencial de venda cross-sectional a partir de um dataset de vendas fornecido (Chilli Beans). A solução proposta segue princípios de transparência, documentação forte (todas as células comentadas em PT-BR) e entrega incremental de artefatos: dados limpos (CSV/Parquet), relatórios de QA, métricas de modelos e gráficos de EDA/Explainability. O fluxo contempla: (1) configuração e padronização de colunas para um esquema canônico; (2) limpeza (tipos, nulos, duplicatas, normalização monetária em centavos); (3) EDA com foco em distribuição temporal, contribuição de lojas e heterogeneidade geográfica / por tipo de PDV; (4) construção de baselines de previsão diária por loja (média móvel 28d e sazonal ingênuo 7d) avaliados por WAPE, sMAPE e MAE em split temporal 80/20; (5) treinamento de um modelo LightGBM para estimar potencial (usar features de idade da loja, geografia, calendário e Tipo_PDV); (6) explainability com SHAP para priorização de drivers; (7) export organizado em `reports/plots` e `reports/tables`; (8) checklist final (Definition of Done) garantindo reprodutibilidade. O horizonte inicial cobre ~5 dias de trabalho com possibilidade de extensão para análises geoespaciais opcionais (choropleth ou ranking+treemap). Limitações e premissas são registradas ao longo do notebook principal.

<b>Link para vídeo demonstrativo (placeholder):</b> a definir.

## 📁 Estrutura de pastas

```
📁 InfoPepper/
├── 📁 assets/                    # Recursos visuais organizados por categoria
│   ├── 📁 business/             # Canvas, SWOT, 5 Forças, Matriz de riscos
│   ├── 📁 diagrams/             # Diagramas técnicos e fluxos
│   ├── 📁 images/               # Imagens gerais (histogramas, logo)
│   └── 📁 personas/             # Fichas e jornadas de personas
├── 📁 config/                   # Arquivos de configuração
├── 📁 data/                     # Dados do projeto
│   ├── 📁 external/             # Dados externos (APIs, etc.)
│   ├── 📁 processed/            # Dados limpos e processados
│   └── 📁 raw/                  # Dados originais (Excel, CSV)
├── 📁 documents/                # Documentos do projeto
│   └── 📁 extras/               # Documentos complementares
├── 📁 notebooks/                # Jupyter notebooks
├── 📁 reports/                  # Relatórios e resultados
│   ├── 📁 exports/              # Relatórios finais
│   ├── 📁 figures/              # Gráficos e visualizações
│   └── 📁 tables/               # Tabelas e métricas
├── 📁 scripts/                  # Scripts de automação e análise
├── 📁 src/                      # Código fonte principal
│   ├── 📁 data/                 # Scripts de processamento de dados
│   ├── 📁 models/               # Scripts de modelagem
│   ├── 📁 utils/                # Utilitários gerais
│   └── 📁 visualization/        # Scripts de visualização
├── 📁 tests/                    # Testes unitários
├── 📁 tools/                    # Ferramentas auxiliares
└── 📁 venv/                     # Ambiente virtual Python
```

### Principais diretórios:

- **README.md**: Guia e explicação geral sobre o projeto
- **assets/**: Recursos visuais organizados por categoria (business, diagrams, images, personas)
- **data/**: Dados organizados por estágio (external, processed, raw)
- **documents/**: Documentos do projeto e extras
- **notebooks/**: notebooks consolidados para todo o fluxo analítico (ver seção abaixo)
- **reports/**: Resultados organizados (exports, figures, tables)
- **scripts/**: Scripts de automação e análise
- **src/**: Código fonte modularizado por funcionalidade
- **tools/**: Ferramentas auxiliares do projeto

## 🔗 Acessos Rápidos

- Estrutura de notebooks consolidada (executar em ordem):
  1) [notebooks/01_data_exploration_analysis.ipynb](notebooks/01_data_exploration_analysis.ipynb)
  2) [notebooks/02_feature_engineering.ipynb](notebooks/02_feature_engineering.ipynb)
  3) [notebooks/03_model_development_comparison.ipynb](notebooks/03_model_development_comparison.ipynb)
  4) [notebooks/model_comparison_colab.ipynb](notebooks/model_comparison_colab.ipynb) (Colab: comparação com tuning e SHAP/importâncias)
  5) [notebooks/04_territorial_analysis.ipynb](notebooks/04_territorial_analysis.ipynb)
  6) [notebooks/05_customer_segmentation.ipynb](notebooks/05_customer_segmentation.ipynb)
  7) [notebooks/06_business_insights_reporting.ipynb](notebooks/06_business_insights_reporting.ipynb)
- **Visualizações principais**:
  - Heatmap (Estado): [reports/figures/heatmap_revenue_by_state.png](reports/figures/heatmap_revenue_by_state.png)
  - Heatmap (Cidade): [reports/figures/heatmap_revenue_by_city.png](reports/figures/heatmap_revenue_by_city.png)
  - Ranking UF: [reports/figures/ranking_uf.png](reports/figures/ranking_uf.png)
  - Correlação (Spearman): [reports/figures/heatmap_corr.png](reports/figures/heatmap_corr.png)
- **Dados processados**: [data/processed/cleaned.parquet](data/processed/cleaned.parquet)
- **Scripts de análise**: [scripts/](scripts/)
- **Código fonte**: [src/](src/)

## 💻 Execução (Ambiente Local VS Code)

1. Criar ambiente virtual
   ```bash
   python -m venv .venv
   .venv\\Scripts\\activate
   ```
2. Instalar dependências principais
   ```bash
   pip install -U pip
   pip install pandas numpy openpyxl matplotlib plotly scikit-learn lightgbm shap pyarrow jupyter
   ```
3. (Opcional) Congelar versão
   ```bash
   pip freeze > requirements.txt
   ```
4. Abrir o notebook principal em `notebooks/chilli_beans_analysis.ipynb` e executar de cima para baixo.

### Execução em Google Colab

1. Abra `notebooks/chilli_beans_analysis_colab.ipynb` ou `notebooks/model_comparison_colab.ipynb` no Colab.
2. Execute a célula de Configuração do ambiente (instala dependências automaticamente).
3. (Opcional) Monte o Google Drive se desejar persistir artefatos fora da sessão.
4. Execute o notebook de cima para baixo. Os artefatos serão salvos em `reports/2025-08-15/` e modelos em `models/`.
2. Fixar versões para reprodutibilidade (célula no início do notebook):
   ````python
   %pip install -q pandas>=1.5.0 numpy>=1.21.0 scipy>=1.9.0 scikit-learn>=1.1.0 seaborn>=0.12.0 matplotlib>=3.5.0
   ```
   ````
3. Carregar arquivo Excel original em `data/raw/` (manter o nome ou atualizar no bloco de Config do notebook).
4. Executar todas as células sequencialmente. Salve uma cópia em seu Drive para preservar alterações.

## Política anti-ID e padronização analítica

Para garantir análises interpretáveis e comparáveis entre regiões e canais, o repositório adota a política anti-ID:

- Evitamos usar colunas de identificação (id_loja, id_cliente, etc.) como dimensões de agrupamento, eixos de gráficos ou filtros analíticos
- IDs são preservados apenas quando necessários para joins/relacionamentos e para contagem de entidades
- Dois utilitários foram introduzidos nos notebooks:
  - SAFE_DIM_OF(df_like): escolhe automaticamente uma dimensão de negócio adequada disponível no dataframe (prioriza uf, cidade, tipo_pdv, estado_emp; caso não exista, tenta a primeira coluna categórica)
  - BUSINESS_ENTITY_DIM(df_like): quando a análise requer contar entidades (ex.: número de lojas por cidade), escolhe a coluna de entidade mais apropriada (id_loja → ou equivalente), evitando outros IDs

**Impacto:** gráficos e métricas passaram a refletir dimensões de negócio (UF, cidade, tipo_pdv), e não identificadores únicos.

## Índice de EDA e relatórios

- Índice de EDA: notebooks/preprocessamento/eda_indice.ipynb
- Visualizações geradas (salvas em reports/2025-08-15/plots/):
  - Série temporal por UF (receita mensal + média móvel)
  - Mix de produtos por UF/tipo_pdv (Top-5 categorias)
  - Heatmap UF x mês (variação % vs média anual)
  - Pareto 80/20 por cidade (receita)

Como navegar:
- Abra o notebook principal: notebooks/chilli_beans_analysis.ipynb
- Para exploração segmentada, use o índice em notebooks/preprocessamento/eda_indice.ipynb


> Observação: sem salvar uma cópia no Drive (File > Save a copy in Drive), mudanças serão perdidas ao encerrar a sessão.

## 📦 Artefatos Gerados (principais)

| Caminho                               | Descrição                             |
| ------------------------------------- | ------------------------------------- |
| `data/processed/cleaned.csv`          | Dataset limpo em CSV                  |
| `data/processed/cleaned.parquet`      | Dataset limpo em Parquet              |
| `reports/tables/qa_overview.csv`      | Resumo de qualidade dos dados         |
| `reports/tables/baseline_metrics.csv` | Métricas dos baselines por loja       |
| `reports/tables/gbm_metrics.csv`      | Métricas do modelo LightGBM           |
| `reports/figures/`                    | Gráficos EDA, baselines, modelo, SHAP |
| `reports/exports/`                    | Relatórios finais exportados          |
| `docs/data_dictionary.md`             | Dicionário de dados canônicos         |

## 🗃 Histórico de lançamentos

- 1.0.0 - 09/10/2025
  - [sprint 5] Lançamento da primeira versão do modelo preditivo com documentação.
- 0.6.0 - 26/09/2025
  - [sprint 4] Comparação de modelos preditivos
- 0.3.1 - 12/09/2025
  - [sprint 3] Preparação de dados e modelo preditivo preliminar
- 0.2.7 - 29/08/2025
  - [sprint 2] Análise exploratória e levantamento de hipóteses
- 0.1.3 - 15/08/2025
  - [sprint 1] Entendimento do negócio

## 📋 Licença/License

<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1"><img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1"><p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/"><a property="dct:title" rel="cc:attributionURL" href="https://github.dev/Intelihub/Template_M3">MODELO GIT INTELI</a> by Inteli is licensed under <a href="http://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Attribution 4.0 International</a>.</p>
