{"cells": [{"cell_type": "code", "execution_count": null, "id": "9506a1c1", "metadata": {}, "outputs": [], "source": ["# [anti-id] helpers – escolher dimensão de negócio em vez de IDs\n", "SAFE_DIM_PRIORITY = ['UF','uf','Cidade','cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n", "def SAFE_DIM_OF(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in SAFE_DIM_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_PRIORITY[0]\n", "# Var global padrão – tenta inferir de df se existir, senão usa primeira opção\n", "try:\n", "    SAFE_DIM = SAFE_DIM_OF(df)\n", "except Exception:\n", "    SAFE_DIM = SAFE_DIM_PRIORITY[0]\n"]}, {"cell_type": "code", "execution_count": null, "id": "37416a1d", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "code", "execution_count": null, "id": "f83ab22a", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "markdown", "id": "202c6d60", "metadata": {}, "source": ["### [auto-doc] Etapa 1\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f77c2fca", "metadata": {}, "outputs": [], "source": ["# [auto-doc] estilo global\n", "import matplotlib as mpl, seaborn as sns\n", "import matplotlib.pyplot as plt\n", "sns.set_theme(style='whitegrid', context='notebook', palette='deep')\n", "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "912fa9d2", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "8762ced6", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON><PERSON> – <PERSON><PERSON>\n", "\n", "Objetivo: executar o pipeline de análise com validações e geração de artefatos."]}, {"cell_type": "markdown", "id": "70e75ec5", "metadata": {}, "source": ["### [auto-doc] Etapa 2\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "67bc0e78", "metadata": {}, "outputs": [], "source": ["import matplotlib as mpl, seaborn as sns\n", "import matplotlib.pyplot as plt\n", "sns.set_theme(style='whitegrid', context='notebook')\n", "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "908af451", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "30404cc2", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>s (Colab)\n", "\n", "> Notebook preparado para execução no Google Colab. Integra os resultados em `reports/2025-08-15/`."]}, {"cell_type": "markdown", "id": "38992633", "metadata": {}, "source": ["### [auto-doc] Etapa 3\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "62d84f56", "metadata": {}, "outputs": [], "source": ["#@title Preparação do Ambiente\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from warnings import filterwarnings; filterwarnings('ignore')\n", "BASE_DIR = Path('.')\n", "# Fallback quando executado a partir da pasta notebooks\n", "if not (BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv').exists():\n", "    BASE_DIR = Path('..')\n", "REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n", "PLOTS_DIR = REPORTS_DIR / 'plots'\n", "EDA_PLOTS_DIR = PLOTS_DIR / 'eda'\n", "TABLES_DIR = REPORTS_DIR / 'tables'\n", "CLEAN_DATA = BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv'\n", "print('PLOTS_DIR:', PLOTS_DIR)\n", "print('EDA_PLOTS_DIR:', EDA_PLOTS_DIR)\n", "print('TABLES_DIR:', TABLES_DIR)\n", "print('CLEAN_DATA:', CLEAN_DATA)\n", "\n", "# Atualiza sys.path para permitir imports do pacote src/\n", "import sys\n", "PROJ_ROOT = BASE_DIR.resolve()\n", "SRC_DIR = PROJ_ROOT / 'src'\n", "for p in [str(PROJ_ROOT), str(SRC_DIR)]:\n", "    if p not in sys.path:\n", "        sys.path.insert(0, p)\n", "print('sys.path atualizado com', SRC_DIR)\n"]}, {"cell_type": "markdown", "id": "ba09ca0a", "metadata": {}, "source": ["### [auto-doc] Etapa 4\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d0e2ef58", "metadata": {}, "outputs": [], "source": ["#@title <PERSON><PERSON><PERSON>ída (plots/tables)\n", "PLOTS_DIR.mkdir(parents=True, exist_ok=True)\n", "EDA_PLOTS_DIR.mkdir(parents=True, exist_ok=True)\n", "TABLES_DIR.mkdir(parents=True, exist_ok=True)\n", "print('Diretórios garantidos:', PLOTS_DIR, EDA_PLOTS_DIR, TABLES_DIR)\n"]}, {"cell_type": "markdown", "id": "21e2dcbd", "metadata": {}, "source": ["### [auto-doc] Etapa 5\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "299b2671", "metadata": {}, "outputs": [], "source": ["#@title Diretório para gráficos de outliers\n", "OUTLIERS_DIR = EDA_PLOTS_DIR / 'outliers'\n", "OUTLIERS_DIR.mkdir(parents=True, exist_ok=True)\n", "print('OUTLIERS_DIR:', OUTLIERS_DIR)\n"]}, {"cell_type": "markdown", "id": "d2cad911", "metadata": {}, "source": ["### [auto-doc] Etapa 6\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8b07b001", "metadata": {}, "outputs": [], "source": ["#@title Teste de imports do pacote src\n", "mods = ['analysis_stats','baselines','features','geo_utils','io_utils','metrics']\n", "for m in mods:\n", "    try:\n", "        __import__(f'src.{m}')\n", "        print(f'OK: src.{m}')\n", "    except Exception as e:\n", "        print(f'FALHA: src.{m} -> {e}')\n"]}, {"cell_type": "markdown", "id": "bece6fc5", "metadata": {}, "source": ["### [auto-doc] Etapa 7\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "65eced5b", "metadata": {}, "outputs": [], "source": ["#@title (Opcional) Fixar versões para reprodutibilidade (Colab)\n", "DO_PIP = False\n", "if DO_PIP:\n", "    import IPython\n", "    IPython.get_ipython().run_line_magic('pip', 'install -q pandas>=1.5.0 numpy>=1.21.0 scipy>=1.9.0 scikit-learn>=1.1.0 seaborn>=0.12.0 matplotlib>=3.5.0 statsmodels>=0.13.0')\n", "import numpy as np\n", "RANDOM_SEED = 42\n", "np.random.seed(RANDOM_SEED)\n", "print('Random seed set to', RANDOM_SEED)\n"]}, {"cell_type": "markdown", "id": "b01b2e39", "metadata": {}, "source": ["## 1. Exploração de Dados (EDA)\n", "- Variáveis numéricas: `valor`, `qtd`\n", "- Categóricas: `uf`, `cidade`, `Tipo_PDV`\n", "- Gráficos: <PERSON><PERSON><PERSON> por UF; <PERSON><PERSON>ita média por dia da semana; Top 20 cidades; Correla<PERSON>pearman (valor×qtd)"]}, {"cell_type": "markdown", "id": "041b0906", "metadata": {}, "source": ["### [auto-doc] Etapa 8\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "85cb83d8", "metadata": {}, "outputs": [], "source": ["#@title Carregamento dos dados limpos\n", "df = pd.read_csv(CLEAN_DATA, parse_dates=['data'])\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f43cb598", "metadata": {}, "outputs": [], "source": ["# [auto-viz] Série temporal por UF (mensal + média móvel)\n", "df_ts = df.copy()\n", "df_ts['ano_mes'] = df_ts['data'].dt.to_period('M').dt.to_timestamp()\n", "kpi = df_ts.groupby(['uf','ano_mes'])['valor'].sum().reset_index()\n", "kpi['mm3'] = kpi.groupby('uf')['valor'].transform(lambda s: s.rolling(3, min_periods=1).mean())\n", "plt.figure(figsize=(10,5))\n", "sns.lineplot(data=kpi, x='ano_mes', y='valor', hue='uf', alpha=0.3)\n", "sns.lineplot(data=kpi, x='ano_mes', y='mm3', hue='uf')\n", "plt.title('<PERSON><PERSON><PERSON> mensal por UF com média móvel (3 meses)')\n", "plt.xlabel('Ano-Mês'); plt.ylabel('<PERSON><PERSON><PERSON>')\n", "plt.legend(title='UF', bbox_to_anchor=(1.05,1), loc='upper left')\n", "plt.tight_layout()\n", "plt.savefig('reports/2025-08-15/plots/serie_temporal_uf.png', dpi=150, bbox_inches='tight')\n", "plt.show()\n", "\n", "# [auto-viz] Mix de produtos por UF/tipo_pdv (top-5 categorias)\n", "df_mix = df.copy()\n", "cat_col = 'categoria' if 'categoria' in df_mix.columns else ('linha' if 'linha' in df_mix.columns else None)\n", "if cat_col is not None and 'tipo_pdv' in df_mix.columns:\n", "    agg = df_mix.groupby(['uf','tipo_pdv',cat_col])['valor'].sum().reset_index()\n", "    def topn(g, n=5):\n", "        g2 = g.sort_values('valor', ascending=False)\n", "        g2['rank'] = g2.groupby(['uf','tipo_pdv']).cumcount()+1\n", "        return g2[g2['rank']<=n]\n", "    top = topn(agg)\n", "    plt.figure(figsize=(10,6))\n", "    sns.barplot(data=top, x='uf', y='valor', hue=cat_col)\n", "    plt.title('Top-5 categorias por UF/tipo_pdv (receita)')\n", "    plt.xlabel('UF'); plt.ylabel('<PERSON><PERSON><PERSON>')\n", "    plt.legend(title='Categoria', bbox_to_anchor=(1.05,1), loc='upper left')\n", "    plt.tight_layout()\n", "    plt.savefig('reports/2025-08-15/plots/mix_produtos_top5.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# [auto-viz] Heatmap UF x mês (variação % vs média anual)\n", "df_hm = df.copy()\n", "df_hm['ano_mes'] = df_hm['data'].dt.to_period('M').dt.to_timestamp()\n", "tab = df_hm.pivot_table(index='uf', columns='ano_mes', values='valor', aggfunc='sum', fill_value=0)\n", "tab_pct = tab.apply(lambda r: (r - r.mean())/r.replace(0, r.mean())*100, axis=1)\n", "plt.figure(figsize=(12,6))\n", "sns.heatmap(tab_pct, cmap='coolwarm', center=0)\n", "plt.title('Variação % da receita por UF vs média anual (por mês)')\n", "plt.xlabel('<PERSON><PERSON><PERSON>'); plt.ylabel('UF')\n", "plt.tight_layout()\n", "plt.savefig('reports/2025-08-15/plots/heatmap_uf_mes_varpct.png', dpi=150, bbox_inches='tight')\n", "plt.show()\n", "\n", "# [auto-viz] Pareto 80/20 por cidade (receita)\n", "df_p = df.copy()\n", "if 'cidade' in df_p.columns:\n", "    city = df_p.groupby('cidade')['valor'].sum().sort_values(ascending=False).reset_index()\n", "    city['cum_pct'] = city['valor'].cumsum()/city['valor'].sum()*100\n", "    plt.figure(figsize=(10,5))\n", "    ax = sns.barplot(data=city.head(30), x='cidade', y='valor', color='tab:blue')\n", "    ax2 = ax.twinx()\n", "    ax2.plot(range(len(city.head(30))), city['cum_pct'].head(30), color='tab:red', marker='o')\n", "    ax2.set_ylabel('% acumulado')\n", "    plt.title('<PERSON><PERSON><PERSON> 80/20 – receita por cidade (top 30)')\n", "    ax.set_xlabel('Cidade'); ax.set_ylabel('<PERSON><PERSON><PERSON>')\n", "    plt.xticks(rotation=80)\n", "    plt.tight_layout()\n", "    plt.savefig('reports/2025-08-15/plots/pareto_cidade.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n"]}, {"cell_type": "markdown", "id": "33256ac4", "metadata": {}, "source": ["### [auto-doc] Etapa 9\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "67369ae8", "metadata": {}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "#@title Estatísticas descritivas e frequências\n", "num_cols = [c for c in ['valor','qtd'] if c in df.columns]\n", "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df.columns]\n", "display(df[num_cols].describe().T)\n", "for c in cat_cols:\n", "    display(pd.DataFrame({'freq': df[c].value_counts().head(10),\n", "                          'pct': df[c].value_counts(normalize=True).head(10)*100}))"]}, {"cell_type": "markdown", "id": "e23716cb", "metadata": {}, "source": ["### Interpretação\n", "\n", "Descreva brevemente os padrões observados no gráfico (tendências, assimetrias, valores atípicos, diferenças entre grupos)\n", "e a implicação prática para o negócio.\n"]}, {"cell_type": "markdown", "id": "c3d6fd0c", "metadata": {}, "source": ["### [auto-doc] Etapa 10\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "55fab98d", "metadata": {}, "outputs": [], "source": ["#@title (EDA) Matriz de correlação (Spearman) e exportação\n", "num_cols_avail = [c for c in ['valor','qtd'] if c in df.columns]\n", "if len(num_cols_avail) >= 2:\n", "    corr = df[num_cols_avail].corr(method='spearman')\n", "    fig = plt.figure(figsize=(5,4))\n", "    sns.heatmap(corr, annot=True, cmap='coolwarm', vmin=-1, vmax=1)\n", "    plt.title('<PERSON><PERSON> (S<PERSON><PERSON>)')\n", "    plt.tight_layout()\n", "    try:\n", "        out_path = PLOTS_DIR / 'heatmap_corr.png'\n", "        fig.savefig(out_path, bbox_inches='tight')\n", "        print('Salvo:', out_path)\n", "    except Exception as e:\n", "        print('Aviso: não foi possível salvar heatmap_corr.png ->', e)\n", "    plt.show()\n", "else:\n", "    print('Variáveis numéricas insuficientes para correlação.')\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "2ea20d71", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "97d848f0", "metadata": {}, "source": ["### [auto-doc] Etapa 11\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "adde60eb", "metadata": {}, "outputs": [], "source": ["#@title Re<PERSON>ita por UF; Receita média por dia; Top 20 cidades\n", "# Receita por UF\n", "revenue_uf = df.groupby('uf')['valor'].sum().sort_values(ascending=False).reset_index()\n", "plt.figure(figsize=(12,5));\n", "sns.barplot(data=revenue_uf, x='uf', y='valor', palette='Blues_r');\n", "plt.title('<PERSON><PERSON><PERSON> por UF (centavos)'); plt.xticks(rotation=45); plt.tight_layout(); plt.show()\n", "# Re<PERSON>ita média por dia da semana\n", "df['dow'] = df['data'].dt.dayofweek\n", "dow_map = {0:'Seg',1:'<PERSON><PERSON>',2:'Qua',3:'Qui',4:'Sex',5:'<PERSON><PERSON><PERSON>',6:'Dom'}\n", "rev_dow = df.groupby('dow')['valor'].mean().rename(index=dow_map).reset_index()\n", "plt.figure(figsize=(8,4)); sns.barplot(data=rev_dow, x='dow', y='valor', palette='viridis');\n", "plt.title('<PERSON><PERSON><PERSON> por <PERSON> Semana'); plt.tight_layout(); plt.show()\n", "# Top 20 cidades\n", "top_cities = df.groupby(['uf','cidade'])['valor'].sum().reset_index().sort_values('valor', ascending=False).head(20)\n", "plt.figure(figsize=(10,8)); sns.barplot(data=top_cities, y='cidade', x='valor', hue='uf', dodge=False, palette='mako');\n", "plt.title('Top 20 Cidades por Receita'); plt.tight_layout(); plt.show()\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "0833ccb7", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "2fe0dd3a", "metadata": {}, "source": ["### 1.1 <PERSON><PERSON><PERSON><PERSON>ivariad<PERSON>\n", "\n", "- Histogramas com escalas e transformações (linear, log1p, sqrt, Box-Cox)\n", "- Boxplots/Violin e densidade\n", "- Exportação para reports/2025-08-15/plots/eda\n"]}, {"cell_type": "markdown", "id": "8da7fd80", "metadata": {}, "source": ["### [auto-doc] Etapa 12\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b0040896", "metadata": {}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "#@title Histogramas e transformações (linear, log1p, sqrt, Box-Cox)\n", "from scipy.stats import boxcox\n", "import numpy as np\n", "num_cols = [c for c in df.select_dtypes(include=['number']).columns if c not in [SAFE_DIM]]\n", "for c in num_cols[:8]:  # limitar para evitar excesso de figuras\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if s.empty:\n", "        continue\n", "    fig, axes = plt.subplots(1, 3, figsize=(15,4))\n", "    sns.histplot(s, kde=True, ax=axes[0]); axes[0].set_title(f'{c} - linear')\n", "    sns.histplot(np.log1p(s.clip(lower=0)), kde=True, ax=axes[1]); axes[1].set_title(f'{c} - log1p')\n", "    sns.histplot(np.sqrt(s.clip(lower=0)), kde=True, ax=axes[2]); axes[2].set_title(f'{c} - sqrt')\n", "    plt.tight_layout()\n", "    try:\n", "        fig.savefig(EDA_PLOTS_DIR / f'hist_{c}_linear_log_sqrt.png', bbox_inches='tight')\n", "    except Exception as e:\n", "        print('Falha ao salvar hist linear/log/sqrt', c, e)\n", "    plt.show()\n", "    # Box-Cox exige valores positivos\n", "    sp = s[s > 0]\n", "    if len(sp) > 10:\n", "        try:\n", "            bc, lam = boxcox(sp)\n", "            plt.figure(figsize=(5,4)); sns.histplot(bc, kde=True); plt.title(f'{c} - Box-Cox (lambda={lam:.2f})');\n", "            plt.tight_layout()\n", "            (EDA_PLOTS_DIR / 'boxcox').mkdir(exist_ok=True)\n", "            plt.savefig(EDA_PLOTS_DIR / 'boxcox' / f'hist_{c}_boxcox.png', bbox_inches='tight')\n", "            plt.show()\n", "        except Exception as e:\n", "            print('Box-<PERSON> indisponível para', c, '->', e)\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "dc0397b6", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "9de7c671", "metadata": {}, "source": ["### [auto-doc] Etapa 13\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e5998134", "metadata": {}, "outputs": [], "source": ["#@title Box/Violin e densidade por variável e categoria\n", "cat_pref = 'Tipo_PDV' if 'Tipo_PDV' in df.columns else ('uf' if 'uf' in df.columns else None)\n", "if cat_pref:\n", "    top_vals = df[cat_pref].value_counts().head(5).index.tolist()\n", "    sub = df[df[cat_pref].isin(top_vals)].copy()\n", "    for c in num_cols[:6]:\n", "        plt.figure(figsize=(10,4));\n", "        plt.subplot(1,2,1); sns.violinplot(data=sub, x=cat_pref, y=c, inner='quartile'); plt.title(f'Violin: {c} por {cat_pref}')\n", "        plt.subplot(1,2,2);\n", "        for v in top_vals:\n", "            sns.kdeplot(pd.to_numeric(sub.loc[sub[cat_pref]==v, c], errors='coerce'), label=str(v))\n", "        plt.legend(); plt.title(f'Densidade: {c} por {cat_pref}')\n", "        plt.tight_layout()\n", "        try:\n", "            plt.savefig(EDA_PLOTS_DIR / f'violin_density_{c}_by_{cat_pref}.png', bbox_inches='tight')\n", "        except Exception as e:\n", "            print('<PERSON><PERSON><PERSON> ao salvar violin/density', c, e)\n", "        plt.show()\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "86150997", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "32d6c9e6", "metadata": {}, "source": ["### 1.2 Correlações Expandida\n", "\n", "- <PERSON>, <PERSON><PERSON><PERSON> e <PERSON>\n", "- Pairplots por categoria (amostragem)\n", "- Clustering hierárquico de correlações\n"]}, {"cell_type": "markdown", "id": "9f03faa9", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "214ef0ff", "metadata": {}, "source": ["### [auto-doc] Etapa 14\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9ed72b1b", "metadata": {}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "#@title Heatmaps de correlação (<PERSON>/<PERSON>/<PERSON>)\n", "num_cols2 = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique() > 2]\n", "methods = ['pearson','spearman','kendall']\n", "for m in methods:\n", "    try:\n", "        corr = df[num_cols2].corr(method=m)\n", "        plt.figure(figsize=(min(12, 2+0.5*len(num_cols2)), min(10, 2+0.5*len(num_cols2))))\n", "        sns.heatmap(corr, cmap='vlag', center=0)\n", "        plt.title(f'<PERSON><PERSON> ({m.title()})')\n", "        plt.tight_layout()\n", "        plt.savefig(EDA_PLOTS_DIR / f'heatmap_corr_{m}.png', bbox_inches='tight')\n", "        plt.show()\n", "    except Exception as e:\n", "        print('Falha correlação', m, '->', e)\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "41b74aee", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "a2fb2e02", "metadata": {}, "source": ["### [auto-doc] Etapa 15\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ca1c8829", "metadata": {}, "outputs": [], "source": ["#@title Pairplot (amostra) segmentado por categoria\n", "try:\n", "    sample = df.sample(min(1500, len(df)), random_state=42) if len(df) > 1500 else df.copy()\n", "    hue = cat_pref if cat_pref in sample.columns else None\n", "    sub_cols = num_cols2[:4]  # limitar\n", "    g = sns.pairplot(sample[sub_cols + ([hue] if hue else [])], hue=hue, diag_kind='kde', corner=True)\n", "    g.fig.suptitle('Pairplot (amostra)'+ (f' por {hue}' if hue else ''), y=1.02)\n", "    g.savefig(EDA_PLOTS_DIR / 'pairplot_sample.png', bbox_inches='tight')\n", "    plt.show()\n", "except Exception as e:\n", "    print('Pairplot indisponível ->', e)\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "583e1cef", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "14cfe67d", "metadata": {}, "source": ["### [auto-doc] Etapa 16\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "efd06b92", "metadata": {}, "outputs": [], "source": ["#@title Clustermap de correlação (Spearman)\n", "try:\n", "    corr_s = df[num_cols2].corr(method='spearman')\n", "    g = sns.clustermap(corr_s, cmap='coolwarm', center=0)\n", "    g.fig.suptitle('<PERSON><PERSON><PERSON><PERSON> (Spearman)')\n", "    g.savefig(EDA_PLOTS_DIR / 'clustermap_spearman.png', bbox_inches='tight')\n", "    plt.show()\n", "except Exception as e:\n", "    print('Clustermap falhou ->', e)\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "7f501fd6", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "7e85284f", "metadata": {}, "source": ["### 1.3 Detecção e Visualização de Outliers\n", "\n", "- Scatterplots com destaque de outliers (robust Z e Mahalanobis)\n", "- Boxplots segmentados por grupos\n", "- Comparativos antes/depois da remoção\n"]}, {"cell_type": "markdown", "id": "5ba05fe4", "metadata": {}, "source": ["### [auto-doc] Etapa 17\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ebac61af", "metadata": {}, "outputs": [], "source": ["#@title Outliers: robust <PERSON> e Mahalanobis\n", "import numpy as np\n", "num_for_out = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>5][:6]\n", "sub = df[num_for_out].dropna()\n", "# Robust Z por coluna\n", "def robust_z_s(x):\n", "    med = np.median(x); mad = np.median(np.abs(x-med));\n", "    return 0.6745*(x-med)/(mad if mad>0 else 1.0)\n", "rz = sub.apply(robust_z_s)\n", "rz_any = (np.abs(rz) >= 3).any(axis=1)\n", "# <PERSON><PERSON><PERSON><PERSON>\n", "X = sub.values\n", "mu = X.mean(axis=0)\n", "cov = np.cov(X, rowvar=False)\n", "# regularização leve para matriz singular\n", "cov += np.eye(cov.shape[0])*1e-6\n", "inv = np.linalg.pinv(cov)\n", "d2 = np.einsum('ij,jk,ik->i', X-mu, inv, X-mu)\n", "thr = np.quantile(d2, 0.995)\n", "mh = d2 >= thr\n", "out_mask = rz_any | mh\n", "# Esco<PERSON>her duas features para scatter\n", "pair = num_for_out[:2] if len(num_for_out)>=2 else num_for_out\n", "if len(pair)==2:\n", "    plt.figure(figsize=(6,5))\n", "    plt.scatter(sub[pair[0]], sub[pair[1]], s=8, alpha=0.3, label='normal')\n", "    plt.scatter(sub.loc[out_mask, pair[0]], sub.loc[out_mask, pair[1]], s=12, color='r', alpha=0.6, label='outlier')\n", "    plt.xlabel(pair[0]); plt.ylabel(pair[1]); plt.title('Scatter com outliers (robustZ/Mahalanobis)'); plt.legend()\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / 'scatter_outliers.png', bbox_inches='tight'); plt.show()\n", "# Antes/depois em uma métrica alvo\n", "target = 'valor' if 'valor' in df.columns else (num_for_out[0] if num_for_out else None)\n", "if target:\n", "    fig, axes = plt.subplots(1,2, figsize=(10,4))\n", "    sns.boxplot(y=df[target], ax=axes[0]); axes[0].set_title(f'{target} (bruto)')\n", "    sns.boxplot(y=sub.loc[~out_mask, target] if target in sub.columns else pd.Series(dtype=float), ax=axes[1]); axes[1].set_title(f'{target} (sem outliers)')\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / f'box_before_after_{target}.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "markdown", "id": "ee82f99f", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "0963821f", "metadata": {}, "source": ["#### 1.3.1 Análise de Normalidade Visual (limitado por amostra)\n", "\n", "Inclui: Q-Q plots, histogramas com curva normal ajustada, testes de normalidade (<PERSON>, <PERSON>) e densidade empírica vs. normal teórica.\n"]}, {"cell_type": "markdown", "id": "83006ce8", "metadata": {}, "source": ["### [auto-doc] Etapa 18\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1136c008", "metadata": {}, "outputs": [], "source": ["#@title Q-Q plots e histograma com curva normal\n", "import numpy as np\n", "from scipy import stats\n", "try:\n", "    import statsmodels.api as sm\n", "except Exception:\n", "    sm = None\n", "num_cols_n = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>10][:6]\n", "for c in num_cols_n:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if len(s) < 20:\n", "        print(f'{c}: amostra muito pequena para avaliação visual robusta (n={len(s)})')\n", "        continue\n", "    # Q-Q plot\n", "    if sm is not None:\n", "        fig = sm.qqplot(s, line='s'); plt.title(f'QQ-plot: {c}');\n", "        fig.savefig(OUTLIERS_DIR / f'qqplot_{c}.png', bbox_inches='tight'); plt.show()\n", "    else:\n", "        print('statsmodels ausente; pulando QQ-plot.')\n", "    # Hist sobreposto com normal teórica\n", "    mu, sigma = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    xs = np.linspace(s.min(), s.max(), 200)\n", "    norm_pdf = stats.norm.pdf(xs, loc=mu, scale=sigma)\n", "    plt.figure(figsize=(6,4))\n", "    sns.histplot(s, kde=False, stat='density', bins='auto', color='steelblue', alpha=0.6)\n", "    plt.plot(xs, norm_pdf, 'r-', label=f'N({mu:.2f}, {sigma:.2f})')\n", "    plt.legend(); plt.title(f'Histograma vs. Normal: {c}')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'hist_normal_{c}.png', bbox_inches='tight'); plt.show()\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "feac3396", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "d72e8480", "metadata": {}, "source": ["### [auto-doc] Etapa 19\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d6b4ffc4", "metadata": {}, "outputs": [], "source": ["#@title Testes de normalidade e densidade empírica vs. normal\n", "from scipy import stats\n", "records = []\n", "for c in num_cols_n:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if len(s) >= 8:\n", "        w_stat, w_p = stats.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "        ad = stats.anderson(s, dist='norm')\n", "        records.append({'col': c, 'n': len(s), 'shapiro_<PERSON>': w_stat, 'shapiro_p': w_p, 'anderson_stat': ad.statistic})\n", "        # Densidade empírica vs. normal\n", "        mu, sigma = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "        xs = np.linspace(s.quantile(0.01), s.quantile(0.99), 200)\n", "        norm_pdf = stats.norm.pdf(xs, loc=mu, scale=sigma)\n", "        plt.figure(figsize=(6,4))\n", "        sns.kdeplot(s, label='Empírica')\n", "        plt.plot(xs, norm_pdf, 'r--', label='Normal teórica')\n", "        plt.legend(); plt.title(f'Densidade empírica vs. normal: {c}')\n", "        plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'kde_vs_normal_{c}.png', bbox_inches='tight'); plt.show()\n", "stats_tbl = pd.DataFrame.from_records(records)\n", "display(stats_tbl)\n", "stats_path = TABLES_DIR / 'normality_tests.csv'\n", "stats_tbl.to_csv(stats_path, index=False)\n", "print('Salvo:', stats_path)\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "d28fe254", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "996b033c", "metadata": {}, "source": ["#### 1.3.2 Outliers com Curvas de Referência\n", "\n", "Inclui: boxplots com limites (IQR, 2-3σ), elipses de confiança bivariadas, resíduos padronizados e marcação de >2-3σ.\n"]}, {"cell_type": "markdown", "id": "02a2d68e", "metadata": {}, "source": ["### [auto-doc] Etapa 20\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "01cc06d9", "metadata": {}, "outputs": [], "source": ["#@title Boxplots com IQR e bandas de desvio-padrão\n", "import numpy as np\n", "cols = num_cols_n[:4] if 'num_cols_n' in globals() else [c for c in df.select_dtypes(include=['number']).columns][:4]\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if s.empty: continue\n", "    q1, q3 = s.quantile(0.25), s.quantile(0.75)\n", "    iqr = q3 - q1\n", "    low_iqr, high_iqr = q1 - 1.5*iqr, q3 + 1.5*iqr\n", "    mu, sd = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    plt.figure(figsize=(7,4))\n", "    sns.boxplot(x=s, orient='h')\n", "    for k, col in [(2,'orange'), (3,'red')]:\n", "        plt.axvline(mu + k*sd, color=col, linestyle='--', alpha=0.7)\n", "        plt.axvline(mu - k*sd, color=col, linestyle='--', alpha=0.7)\n", "    plt.axvspan(low_iqr, high_iqr, color='green', alpha=0.08, label='±1.5 IQR')\n", "    plt.title(f'Boxplot {c} com limites teóricos (±1.5 IQR, ±2σ, ±3σ)')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'box_theoretical_{c}.png', bbox_inches='tight'); plt.show()\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "9627819f", "metadata": {}, "source": ["### Interpretação\n", "\n", "Descreva brevemente os padrões observados no gráfico (tendências, assimetrias, valores atípicos, diferenças entre grupos)\n", "e a implicação prática para o negócio.\n"]}, {"cell_type": "markdown", "id": "41307c83", "metadata": {}, "source": ["### [auto-doc] Etapa 21\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f878b2e7", "metadata": {}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "#@title Scatter com elipses de confiança bivariadas\n", "from matplotlib.patches import Ellipse\n", "pair = None\n", "nums = [c for c in df.select_dtypes(include=['number']).columns]\n", "if len(nums)>=2: pair = nums[:2]\n", "if pair:\n", "    x = pd.to_numeric(df[pair[0]], errors='coerce').dropna()\n", "    y = pd.to_numeric(df[pair[1]], errors='coerce').dropna()\n", "    n = min(len(x), len(y))\n", "    x, y = x.iloc[:n], y.iloc[:n]\n", "    X = np.column_stack([x, y])\n", "    mu = X.mean(axis=0); cov = np.cov(X, rowvar=False) + np.eye(2)*1e-6\n", "    vals, vecs = np.linalg.eigh(cov)\n", "    order = vals.argsort()[::-1]\n", "    vals, vecs = vals[order], vecs[:, order]\n", "    theta = np.degrees(np.arctan2(*vecs[:,0][::-1]))\n", "    fig, ax = plt.subplots(figsize=(6,5))\n", "    ax.scatter(x, y, s=8, alpha=0.3)\n", "    # Elipses ~ 1σ, 2σ, 3σ (aprox)\n", "    for k, col in [(1,'#2ca02c'),(2,'#ff7f0e'),(3,'#d62728')]:\n", "        width, height = 2*k*np.sqrt(vals)\n", "        ell = Ellipse(xy=mu, width=width, height=height, angle=theta, edgecolor=col, facecolor='none', lw=2, alpha=0.7)\n", "        ax.add_patch(ell)\n", "    ax.set_xlabel(pair[0]); ax.set_ylabel(pair[1]); ax.set_title('Elipses de confiança bivariadas (aprox)')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'scatter_ellipses.png', bbox_inches='tight'); plt.show()\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "7d654275", "metadata": {}, "source": ["### Interpretação\n", "\n", "Descreva brevemente os padrões observados no gráfico (tendências, assimetrias, valores atípicos, diferenças entre grupos)\n", "e a implicação prática para o negócio.\n"]}, {"cell_type": "markdown", "id": "b856c5a7", "metadata": {}, "source": ["### [auto-doc] Etapa 22\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d6341a79", "metadata": {}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "#@title Resíduos padronizados e marcação de >2-3σ\n", "from sklearn.linear_model import LinearRegression\n", "nums = [c for c in df.select_dtypes(include=['number']).columns]\n", "if len(nums)>=2:\n", "    X = pd.to_numeric(df[nums[0]], errors='coerce').values.reshape(-1,1)\n", "    y = pd.to_numeric(df[nums[1]], errors='coerce').values\n", "    mask = ~np.isnan(X).ravel() & ~np.isnan(y)\n", "    X, y = X[mask], y[mask]\n", "    if len(y) > 10:\n", "        lr = LinearRegression().fit(X, y)\n", "        res = y - lr.predict(X)\n", "        s_res = (res - res.mean())/(res.std(ddof=1) if res.std(ddof=1)>0 else 1.0)\n", "        fig, ax = plt.subplots(1,2, figsize=(11,4))\n", "        ax[0].scatter(X, res, s=8, alpha=0.4); ax[0].axhline(0, color='k', lw=1)\n", "        for k,col in [(2,'orange'),(3,'red')]: ax[0].axhline(k*res.std(ddof=1), color=col, ls='--'); ax[0].axhline(-k*res.std(ddof=1), color=col, ls='--')\n", "        ax[0].set_title('Resíduos com bandas ±2σ/±3σ')\n", "        ax[1].hist(s_res, bins='auto', density=True, alpha=0.6); xs = np.linspace(s_res.min(), s_res.max(), 200); ax[1].plot(xs, stats.norm.pdf(xs, 0, 1), 'r--'); ax[1].set_title('Resíduos padronizados vs. N(0,1)')\n", "        plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'residuos_padronizados.png', bbox_inches='tight'); plt.show()\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "3fda3312", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "cb9c8e22", "metadata": {}, "source": ["### [auto-doc] Etapa 23\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "86045bfe", "metadata": {}, "outputs": [], "source": ["#@title Filtragem progressiva e impacto nas estatísticas\n", "import numpy as np\n", "cols = num_cols_n if 'num_cols_n' in globals() else [c for c in df.select_dtypes(include=['number']).columns][:6]\n", "summary = []\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if len(s) < 10: continue\n", "    mu, sd = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    q1, q3 = s.quantile(0.25), s.quantile(0.75); iqr = q3 - q1\n", "    cut_sd2 = s[(s >= mu-2*sd) & (s <= mu+2*sd)]\n", "    cut_sd3 = s[(s >= mu-3*sd) & (s <= mu+3*sd)]\n", "    cut_iqr = s[(s >= q1-1.5*iqr) & (s <= q3*****iqr)]\n", "    fig, axes = plt.subplots(1,2, figsize=(11,4))\n", "    sns.kdeplot(s, ax=axes[0], label='Bruto'); sns.kdeplot(cut_iqr, ax=axes[0], label='±1.5 IQR'); sns.kdeplot(cut_sd2, ax=axes[0], label='±2σ'); sns.kdeplot(cut_sd3, ax=axes[0], label='±3σ')\n", "    axes[0].legend(); axes[0].set_title(f'{c}: densidades (bruto vs filtros)')\n", "    axes[1].boxplot([s, cut_iqr, cut_sd2, cut_sd3], labels=['bruto','±1.5IQR','±2σ','±3σ'], vert=False)\n", "    axes[1].set_title(f'{c}: boxplots comparativos')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'filters_compare_{c}.png', bbox_inches='tight'); plt.show()\n", "    summary.append({'col': c, 'n_bruto': len(s), 'n_iqr': len(cut_iqr), 'n_sd2': len(cut_sd2), 'n_sd3': len(cut_sd3)})\n", "tbl = pd.DataFrame(summary)\n", "tbl['pct_remov_iqr'] = 1 - tbl['n_iqr']/tbl['n_bruto']\n", "tbl['pct_remov_sd2'] = 1 - tbl['n_sd2']/tbl['n_bruto']\n", "tbl['pct_remov_sd3'] = 1 - tbl['n_sd3']/tbl['n_bruto']\n", "display(tbl)\n", "tbl_path = TABLES_DIR / 'outlier_filtering_impact.csv'\n", "tbl.to_csv(tbl_path, index=False)\n", "print('Salvo:', tbl_path)\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "00618117", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "f3ba8a65", "metadata": {}, "source": ["##### Observações para dataset limitado\n", "\n", "- Prefira limiares robustos (MAD/percentis) quando a normalidade é fraca.\n", "- Use bootstrap para ICs de estatísticas e avalie sensibilidade a n.\n", "- Documente trade-offs entre remover outliers e perda de informação.\n"]}, {"cell_type": "markdown", "id": "954636c6", "metadata": {}, "source": ["#### 1.3.3 Bootstrap de ICs (média e mediana)\n", "\n", "- 2000 reamostragens por variável (ajustável)\n", "- ICs 95% por percentis (2.5%, 97.5%)\n", "- Comparação com ICs paramétricos assumindo normalidade\n"]}, {"cell_type": "markdown", "id": "7c255e7a", "metadata": {}, "source": ["### [auto-doc] Etapa 24\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "4e7bb1cb", "metadata": {}, "outputs": [], "source": ["#@title Bootstrap de ICs (média e mediana) + comparação paramétrica\n", "import numpy as np, math, json\n", "from scipy.stats import norm\n", "N_BOOT = 2000\n", "cols = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>=5]\n", "rows = []\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna().values\n", "    n = len(s)\n", "    if n < 20:\n", "        continue\n", "    # Bootstrap\n", "    rng = np.random.default_rng(42)\n", "    idx = rng.integers(0, n, size=(N_BOOT, n))\n", "    samples = s[idx]\n", "    boot_means = samples.mean(axis=1)\n", "    boot_medians = np.median(samples, axis=1)\n", "    m, md = float(s.mean()), float(np.median(s))\n", "    sd = float(s.std(ddof=1)) if s.std(ddof=1)>0 else 1.0\n", "    se_mean = sd/math.sqrt(n)\n", "    z = 1.96\n", "    # Paramétrico: média\n", "    mean_low_p, mean_high_p = m - z*se_mean, m + z*se_mean\n", "    # Paramétrico (aprox) para mediana: sd_median ≈ 1.253*sd/sqrt(n)\n", "    se_median = 1.253*sd/math.sqrt(n)\n", "    med_low_p, med_high_p = md - z*se_median, md + z*se_median\n", "    # Bootstrap percentis\n", "    mean_low_b, mean_high_b = np.percentile(boot_means, [2.5, 97.5])\n", "    med_low_b, med_high_b = np.percentile(boot_medians, [2.5, 97.5])\n", "    rows.append({\n", "        'col': c, 'n': n, 'mean': m, 'mean_ci_boot_low': mean_low_b, 'mean_ci_boot_high': mean_high_b,\n", "        'mean_ci_param_low': mean_low_p, 'mean_ci_param_high': mean_high_p,\n", "        'median': md, 'median_ci_boot_low': med_low_b, 'median_ci_boot_high': med_high_b,\n", "        'median_ci_param_low': med_low_p, 'median_ci_param_high': med_high_p,\n", "        'n_boot': N_BOOT\n", "    })\n", "boot_tbl = pd.DataFrame(rows)\n", "display(boot_tbl.head())\n", "boot_path = TABLES_DIR / 'bootstrap_confidence_intervals.csv'\n", "boot_tbl.to_csv(boot_path, index=False)\n", "print('Salvo:', boot_path)"]}, {"cell_type": "markdown", "id": "62434cae", "metadata": {}, "source": ["### [auto-doc] Etapa 25\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0bab69e9", "metadata": {}, "outputs": [], "source": ["#@title Visualização dos ICs (barras de erro)\n", "if 'boot_tbl' in globals() and not boot_tbl.empty:\n", "    top = boot_tbl.sort_values('n', ascending=False).head(8)\n", "    # Médias\n", "    plt.figure(figsize=(10,5))\n", "    x = np.arange(len(top))\n", "    y = top['mean'].values\n", "    yerr = np.vstack([y - top['mean_ci_boot_low'].values, top['mean_ci_boot_high'].values - y])\n", "    plt.errorbar(x, y, yerr=yerr, fmt='o', capsize=4, label='IC bootstrap')\n", "    yerr_p = np.vstack([y - top['mean_ci_param_low'].values, top['mean_ci_param_high'].values - y])\n", "    plt.errorbar(x, y, yerr=yerr_p, fmt='s', capsize=4, label='IC paramétrico', alpha=0.7)\n", "    plt.xticks(x, top['col'].tolist(), rotation=45)\n", "    plt.title('ICs 95% da média (bootstrap vs paramétrico)')\n", "    plt.legend(); plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'bootstrap_ics_means.png', bbox_inches='tight'); plt.show()\n", "    # Medianas\n", "    plt.figure(figsize=(10,5))\n", "    y2 = top['median'].values\n", "    yerr2 = np.vstack([y2 - top['median_ci_boot_low'].values, top['median_ci_boot_high'].values - y2])\n", "    plt.errorbar(x, y2, yerr=yerr2, fmt='o', capsize=4, label='IC bootstrap')\n", "    yerr2p = np.vstack([y2 - top['median_ci_param_low'].values, top['median_ci_param_high'].values - y2])\n", "    plt.errorbar(x, y2, yerr=yerr2p, fmt='s', capsize=4, label='IC paramétrico (aprox)', alpha=0.7)\n", "    plt.xticks(x, top['col'].tolist(), rotation=45)\n", "    plt.title('ICs 95% da mediana (bootstrap vs paramétrico aprox)')\n", "    plt.legend(); plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'bootstrap_ics_medians.png', bbox_inches='tight'); plt.show()\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "ba4f5389", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "d68d1b2b", "metadata": {}, "source": ["#### 1.3.4 Sumário Orientativo e Configuração para Pipeline\n", "\n", "- Recomendações por variável (sigma vs MAD/IQR) a partir de Shapiro p\n", "- Geração de config JSON reutilizável\n", "- Visualização de % outliers por método e concordância entre métodos\n"]}, {"cell_type": "markdown", "id": "201e8757", "metadata": {}, "source": ["### [auto-doc] Etapa 26\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "4cbe6370", "metadata": {}, "outputs": [], "source": ["#@title Recomendações por normalidade (Shapiro) e config para pipeline\n", "import json, numpy as np\n", "# Reusar resultados de normalidade se existirem; caso con<PERSON><PERSON><PERSON>, calcular <PERSON> m<PERSON>\n", "if 'stats_tbl' in globals() and not stats_tbl.empty:\n", "    norm_df = stats_tbl[['col','shapiro_p']].copy()\n", "else:\n", "    cols = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>=5]\n", "    recs = []\n", "    for c in cols:\n", "        s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "        if len(s) >= 8:\n", "            w_stat, w_p = stats.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "            recs.append({'col': c, 'shapiro_p': float(w_p)})\n", "    norm_df = pd.DataFrame(recs)\n", "# Regras\n", "def recommend(p):\n", "    return 'sigma' if (p is not None and p > 0.05) else 'robust'\n", "norm_df['method'] = norm_df['shapiro_p'].apply(recommend)\n", "# <PERSON><PERSON> recomendado\n", "cfg_rows = []\n", "for _, r in norm_df.iterrows():\n", "    c = r['col']; method = r['method']; p = r['shapiro_p']\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if method == 'sigma':\n", "        mu, sd = float(s.mean()), float(s.std(ddof=1) if s.std(ddof=1)>0 else 1.0)\n", "        lim = {'low': mu - 3*sd, 'high': mu + 3*sd}\n", "        just = f'p={p:.3f} > 0.05; usar ±3σ (assumindo normalidade aproximada)'\n", "    else:\n", "        q1, q3 = float(s.quantile(0.25)), float(s.quantile(0.75)); iqr = q3 - q1\n", "        low, high = q1 - 1.5*iqr, q3 + 1.5*iqr\n", "        just = f'p={p:.3f} ≤ 0.05; usar IQR (robusto)'; lim = {'low': low, 'high': high}\n", "    cfg_rows.append({'col': c, 'method': ('sigma' if method=='sigma' else 'iqr'), 'threshold': lim, 'justification': just})\n", "rec_tbl = pd.DataFrame(cfg_rows)\n", "display(rec_tbl)\n", "rec_path = TABLES_DIR / 'outlier_method_recommendations.csv'\n", "rec_tbl.to_csv(rec_path, index=False)\n", "print('Salvo:', rec_path)\n", "# JSON de configuração\n", "cfg = {row['col']: {'method': row['method'], 'threshold': row['threshold'], 'justification': row['justification']} for _, row in rec_tbl.iterrows()}\n", "cfg_path = TABLES_DIR / 'preprocessing_config.json'\n", "with open(cfg_path, 'w', encoding='utf-8') as f:\n", "    json.dump(cfg, f, ensure_ascii=False, indent=2)\n", "print('Salvo:', cfg_path)"]}, {"cell_type": "markdown", "id": "9e8af95a", "metadata": {}, "source": ["### [auto-doc] Etapa 27\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "15ca461f", "metadata": {}, "outputs": [], "source": ["#@title Visualização consolidada: % outliers por método e concordância\n", "import numpy as np\n", "cols = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>=5][:10]\n", "summary = []\n", "agree_rows = []\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    n = len(s)\n", "    if n < 20: continue\n", "    # sigma\n", "    mu, sd = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    mask2 = (s < mu-2*sd) | (s > mu+2*sd)\n", "    mask3 = (s < mu-3*sd) | (s > mu+3*sd)\n", "    # iqr\n", "    q1, q3 = s.quantile(0.25), s.quantile(0.75); iqr = q3 - q1\n", "    mask_iqr = (s < q1-1.5*iqr) | (s > q3*****iqr)\n", "    summary.append({'col': c, '%_2sigma': mask2.mean(), '%_3sigma': mask3.mean(), '%_iqr': mask_iqr.mean()})\n", "    # concord<PERSON><PERSON> (<PERSON><PERSON><PERSON>) entre métodos\n", "    def jacc(a, b):\n", "        inter = np.logical_and(a, b).sum()\n", "        uni = np.logical_or(a, b).sum()\n", "        return inter/uni if uni>0 else 1.0\n", "    agree_rows.append({'col': c, 'J(2σ,3σ)': jacc(mask2, mask3), 'J(2σ,IQR)': jacc(mask2, mask_iqr), 'J(3σ,IQR)': jacc(mask3, mask_iqr)})\n", "sum_tbl = pd.DataFrame(summary)\n", "display(sum_tbl)\n", "sum_path = TABLES_DIR / 'outlier_detection_rates.csv'\n", "sum_tbl.to_csv(sum_path, index=False)\n", "print('Salvo:', sum_path)\n", "agree_tbl = pd.DataFrame(agree_rows).set_index('col')\n", "plt.figure(figsize=(6,4))\n", "sns.heatmap(agree_tbl, annot=True, vmin=0, vmax=1, cmap='YlGnBu')\n", "plt.title('<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) entre métodos de outlier')\n", "plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'outlier_methods_agreement.png', bbox_inches='tight'); plt.show()\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "5a4a41c7", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "cd509579", "metadata": {}, "source": ["### 1.4 Correlações Parciais (controle de confundidores)\n", "\n", "- Controle por 'qtd' quando disponível (correlação de Pearson nos resíduos)\n"]}, {"cell_type": "markdown", "id": "09a8760c", "metadata": {}, "source": ["### [auto-doc] Etapa 28\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "c1677ab7", "metadata": {}, "outputs": [], "source": ["#@title Correlações parciais controlando por 'qtd' (se existir)\n", "import numpy as np\n", "conf = 'qtd' if 'qtd' in df.columns else None\n", "num_cols3 = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>5]\n", "if conf and conf in num_cols3:\n", "    cols = [c for c in num_cols3 if c != conf][:10]\n", "    M = df[[conf]].fillna(0).values\n", "    def resid(y, X):\n", "        X1 = np.hstack([np.ones((len(X),1)), X])\n", "        beta, *_ = np.linalg.lstsq(X1, y, rcond=None)\n", "        return y - X1@beta\n", "    R = np.zeros((len(cols), len(cols)))\n", "    for i, ci in enumerate(cols):\n", "        yi = pd.to_numeric(df[ci], errors='coerce').fillna(0).values.reshape(-1,1)\n", "        ri = resid(yi, M).ravel()\n", "        for j, cj in enumerate(cols):\n", "            yj = pd.to_numeric(df[cj], errors='coerce').fillna(0).values.reshape(-1,1)\n", "            rj = resid(yj, M).ravel()\n", "            if i==j:\n", "                R[i,j] = 1.0\n", "            else:\n", "                R[i,j] = np.corrcoef(ri, rj)[0,1]\n", "    pcorr = pd.DataFrame(R, index=cols, columns=cols)\n", "    plt.figure(figsize=(min(12, 2+0.5*len(cols)), min(10, 2+0.5*len(cols))))\n", "    sns.heatmap(pcorr, cmap='vlag', center=0)\n", "    \n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / 'partial_corr_control_qtd.png', bbox_inches='tight'); plt.show()\n", "else:\n", "    print('Sem variável de controle numérica adequada (ex.: qtd).')\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "670e7ae7", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "136640e4", "metadata": {}, "source": ["### 1.5 Visualizações Comparativas por Grupos\n", "\n", "- Comparações de distribuições entre segmentos relevantes\n"]}, {"cell_type": "markdown", "id": "23cf1af0", "metadata": {}, "source": ["### [auto-doc] Etapa 29\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "4d233803", "metadata": {}, "outputs": [], "source": ["#@title ECDF/KDE comparando grupos\n", "cat_pref = 'Tipo_PDV' if 'Tipo_PDV' in df.columns else ('uf' if 'uf' in df.columns else None)\n", "metric = 'valor' if 'valor' in df.columns else (num_cols3[0] if num_cols3 else None)\n", "if cat_pref and metric:\n", "    vals = df[cat_pref].value_counts().head(4).index.tolist()\n", "    sub = df[df[cat_pref].isin(vals)].copy()\n", "    plt.figure(figsize=(8,5))\n", "    for v in vals:\n", "        s = pd.to_numeric(sub.loc[sub[cat_pref]==v, metric], errors='coerce').dropna()\n", "        sns.ecdfplot(s, label=str(v))\n", "    plt.legend(); plt.title(f'ECDF de {metric} por {cat_pref}')\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / f'ecdf_{metric}_by_{cat_pref}.png', bbox_inches='tight'); plt.show()\n", "    plt.figure(figsize=(8,5))\n", "    for v in vals:\n", "        s = pd.to_numeric(sub.loc[sub[cat_pref]==v, metric], errors='coerce').dropna()\n", "        sns.kdeplot(s, label=str(v), fill=False)\n", "    plt.legend(); plt.title(f'KDE de {metric} por {cat_pref}')\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / f'kde_{metric}_by_{cat_pref}.png', bbox_inches='tight'); plt.show()\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "130cd67b", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "98c0258b", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "- Nulos; Outliers (MAD) + winsorização; Codificação (OneHot); Normalização (StandardScaler)"]}, {"cell_type": "markdown", "id": "7aaeb1f9", "metadata": {}, "source": ["### [auto-doc] Etapa 30\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "53fb693f", "metadata": {}, "outputs": [], "source": ["#@title Limpeza de nulos e preparação\n", "df_pp = df.copy()\n", "if 'qtd' in df_pp.columns:\n", "    df_pp['qtd'] = df_pp['qtd'].fillna(0)\n", "df_pp = df_pp.dropna(subset=['data',SAFE_DIM_OF(df_pp)])\n", "df_pp.shape\n"]}, {"cell_type": "markdown", "id": "f4376610", "metadata": {}, "source": ["### [auto-doc] Etapa 31\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "669d973c", "metadata": {}, "outputs": [], "source": ["#@title Outliers (MAD) e winsorização\n", "import numpy as np\n", "def robust_z(x):\n", "    med = x.median(); mad = (x-med).abs().median()\n", "    if mad == 0: return pd.Series(np.zeros(len(x)), index=x.index)\n", "    return 0.6745*(x - med)/mad\n", "df_pp['rz'] = df_pp.groupby(SAFE_DIM_OF(df_pp))['valor'].transform(robust_z)\n", "outliers = df_pp[df_pp['rz'].abs() >= 3]\n", "cap_low = df_pp['valor'].quantile(0.01)\n", "cap_high = df_pp['valor'].quantile(0.99)\n", "df_pp['valor_winsor'] = df_pp['valor'].clip(lower=cap_low, upper=cap_high)\n", "df_pp[['valor','valor_winsor']].describe().T\n"]}, {"cell_type": "markdown", "id": "c3a9aac2", "metadata": {}, "source": ["### [auto-doc] Etapa 32\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a1f3343b", "metadata": {}, "outputs": [], "source": ["#@title Codificação e normalização\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df_pp.columns]\n", "num_cols = [c for c in ['valor_winsor','qtd'] if c in df_pp.columns]\n", "ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False)\n", "X_cat = ohe.fit_transform(df_pp[cat_cols]) if cat_cols else np.empty((len(df_pp),0))\n", "scaler = StandardScaler()\n", "X_num = scaler.fit_transform(df_pp[num_cols]) if num_cols else np.empty((len(df_pp),0))\n", "X = np.hstack([X_num, X_cat]); X.shape\n"]}, {"cell_type": "markdown", "id": "0e9e1e9a", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON><PERSON> (H1–H3)\n", "H1: SP > demais <PERSON> (receita por loja)\n", "\n", "H2: <PERSON><PERSON> de semana ≠ dias ú<PERSON> (receita diária)\n", "\n", "H3: Cidades com múltiplas lojas > lojas únicas (total e por loja)"]}, {"cell_type": "markdown", "id": "816d818c", "metadata": {}, "source": ["### [auto-doc] Etapa 33\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "784b2c28", "metadata": {}, "outputs": [], "source": ["#@title Testes H1–H3 (<PERSON><PERSON>)\n", "from scipy import stats\n", "# H1: receita média por loja por UF (base cidade)\n", "city_store = df.groupby(['uf','cidade'])[BUSINESS_ENTITY_DIM(df)].nunique().reset_index(name='n_lojas')\n", "city_rev = df.groupby(['uf','cidade'])['valor'].sum().reset_index(name='receita_total')\n", "city = city_store.merge(city_rev, on=['uf','cidade'])\n", "city['receita_por_loja'] = city['receita_total']/city['n_lojas']\n", "sp_vals = city.loc[city['uf']=='SP','receita_por_loja']\n", "others = city.loc[city['uf']!='SP','receita_por_loja']\n", "if len(sp_vals)>0 and len(others)>0:\n", "    stat1, p1 = stats.mannwhitneyu(sp_vals, others, alternative='greater')\n", "    print(f'H1 - <PERSON><PERSON><PERSON> (SP > outras UFs): U={stat1:.2f} p={p1:.4g}')\n", "# H2: fim de semana vs dias <PERSON><PERSON><PERSON>\n", "df['is_weekend'] = df['data'].dt.dayofweek >= 5\n", "dw_daily = df.groupby(['data','is_weekend'])['valor'].sum().reset_index()\n", "weekend = dw_daily.loc[dw_daily['is_weekend']==True,'valor']\n", "weekday = dw_daily.loc[dw_daily['is_weekend']==False,'valor']\n", "if len(weekend)>0 and len(weekday)>0:\n", "    stat2, p2 = stats.mannwhitneyu(weekend, weekday, alternative='two-sided')\n", "    print(f'H2 - <PERSON><PERSON><PERSON> (fim de semana != dias <PERSON><PERSON><PERSON>): U={stat2:.2f} p={p2:.4g}')\n", "# H3: cidades multi-loja vs single-loja\n", "multi = city.loc[city['n_lojas']>1]\n", "single = city.loc[city['n_lojas']==1]\n", "if len(multi)>0 and len(single)>0:\n", "    s3a, p3a = stats.mannwhitneyu(multi['receita_total'], single['receita_total'], alternative='greater')\n", "    s3b, p3b = stats.mannwhitneyu(multi['receita_por_loja'], single['receita_por_loja'], alternative='greater')\n", "    print(f'H3 - total (multi>single): U={s3a:.2f} p={p3a:.4g}')\n", "    print(f'H3 - por loja (multi>single): U={s3b:.2f} p={p3b:.4g}')\n"]}, {"cell_type": "markdown", "id": "4482b53c", "metadata": {}, "source": ["### [auto-doc] Etapa 34\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e568e41a", "metadata": {}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "#@title Diagnóstico de distribuição e tamanho de efeito (delta de Cliff)\n", "import scipy.stats as ss\n", "try:\n", "    import statsmodels.api as sm\n", "except Exception:\n", "    sm = None\n", "def cliffs_delta(x, y):\n", "    x = pd.Series(x).dropna().values; y = pd.Series(y).dropna().values\n", "    gt = sum(xx > yy for xx in x for yy in y)\n", "    lt = sum(xx < yy for xx in x for yy in y)\n", "    n = len(x)*len(y)\n", "    return (gt - lt)/n if n>0 else float('nan')\n", "samples = {\n", "    'SP por loja': sp_vals,\n", "    'Outras UFs por loja': others,\n", "    'Re<PERSON>ita diária fim de semana': weekend,\n", "    'Re<PERSON>ita diária dia <PERSON>': weekday\n", "}\n", "for name, s in samples.items():\n", "    s = pd.Series(s).dropna()\n", "    if len(s) > 8:\n", "        stat, p = ss.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "        print(f'{name}: <PERSON><PERSON><PERSON><PERSON><PERSON> p={p:.4g}')\n", "        \n", "        if sm is not None:\n", "            sm.qqplot(s, line='s'); plt.title(f'QQ-plot: {name}'); plt.show()\n", "        else:\n", "            print('statsmodels ausente; pulando QQ-plot.')\n", "# Cliff's delta\n", "if len(sp_vals)>0 and len(others)>0:\n", "    print(f\"H1 - Cliff's delta: {cliffs_delta(sp_vals, others):.3f}\")\n", "if len(weekend)>0 and len(weekday)>0:\n", "    print(f\"H2 - Cliff's delta: {cliffs_delta(weekend, weekday):.3f}\")\n", "if len(multi)>0 and len(single)>0:\n", "    print(f\"H3 - Cliff's delta (total): {cliffs_delta(multi['receita_total'], single['receita_total']):.3f}\")\n", "    print(f\"H3 - Cliff's delta (por loja): {cliffs_delta(multi['receita_por_loja'], single['receita_por_loja']):.3f}\")\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "6e181ae2", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "43a8cf54", "metadata": {}, "source": ["### [auto-doc] Etapa 35\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e5df9b0", "metadata": {}, "outputs": [], "source": ["#@title <PERSON><PERSON><PERSON> (p-valores ajustados)\n", "import numpy as np\n", "pvals = []\n", "labels = []\n", "if 'p1' in globals(): pvals.append(p1); labels.append('H1')\n", "if 'p2' in globals(): pvals.append(p2); labels.append('H2')\n", "if 'p3a' in globals() and 'p3b' in globals(): pvals.append(max(p3a, p3b)); labels.append('H3 (maior p)')\n", "def holm_bonferroni(ps):\n", "    m = len(ps); order = np.argsort(ps); adj = np.empty(m)\n", "    for i, idx in enumerate(order):\n", "        adj[idx] = min((m - i) * ps[idx], 1.0)\n", "    return adj\n", "if pvals:\n", "    adj = holm_bonferroni(pvals)\n", "    for lab, raw, ap in zip(labels, pvals, adj):\n", "        print(f'{lab}: bruto={raw:.4g} | ajustado(Holm)={ap:.4g}')\n"]}, {"cell_type": "markdown", "id": "2f2e1088", "metadata": {}, "source": ["## 4. Integração com Resultados Existentes\n", "- Referência aos artefatos em `reports/2025-08-15/` (16 PNGs, 18 CSVs)"]}, {"cell_type": "markdown", "id": "62120998", "metadata": {}, "source": ["### [auto-doc] Etapa 36\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7ad80750", "metadata": {}, "outputs": [], "source": ["#@title Leitura de tabelas geradas (exemplos)\n", "import os\n", "tables = sorted([p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')])[:5]\n", "print('Exemplos CSV:', tables)\n", "if (TABLES_DIR / 'summary.csv').exists():\n", "    example = pd.read_csv(TABLES_DIR / 'summary.csv')\n", "    display(example.head())\n"]}, {"cell_type": "markdown", "id": "f86ee4e3", "metadata": {}, "source": ["## 5. Validação automática e checklist final\n", "Valida PNG=16 e CSV=18 e lista diretórios vazios."]}, {"cell_type": "markdown", "id": "47031a2a", "metadata": {}, "source": ["### [auto-doc] Etapa 37\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "99a74328", "metadata": {}, "outputs": [], "source": ["#@title Validação automática dos artefatos e diretórios vazios\n", "import os\n", "pngs = [p for p in os.listdir(PLOTS_DIR) if p.endswith('.png')]\n", "csvs = [p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')]\n", "print('PNG files:', len(pngs), ' (esperado: 16)')\n", "print('CSV files:', len(csvs), ' (esperado: 18)')\n", "empty_dirs = []\n", "for root, dirs, files in os.walk('.'):\n", "    for d in dirs:\n", "        full = Path(root) / d\n", "        try:\n", "            if len(list((full).iterdir())) == 0:\n", "                empty_dirs.append(str(full))\n", "        except Exception:\n", "            pass\n", "print('<PERSON><PERSON><PERSON><PERSON><PERSON> vazios:', empty_dirs if empty_dirs else 'nenhum')\n"]}, {"cell_type": "markdown", "id": "1c6fa7dd", "metadata": {}, "source": ["## 6. Modelo Final e Justificativa\n", "\n", "Nesta seção, carregamos os resultados da comparação de modelos, selecionamos o vencedor com base nas métricas (priorizando menor RMSE e/ou maior R²), re‑treinamos no conjunto completo e salvamos o modelo final para uso posterior."]}, {"cell_type": "markdown", "id": "a7f71088", "metadata": {}, "source": ["### [auto-doc] Etapa 38\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7491d965", "metadata": {}, "outputs": [], "source": ["#@title <PERSON><PERSON><PERSON>, re‑treino e salvamento do modelo final (com justificativa)\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.svm import SVR\n", "import json, joblib\n", "\n", "# Diret<PERSON><PERSON><PERSON>ída (herdados do notebook principal)\n", "try:\n", "    REPORTS_DIR, PLOTS_DIR, TABLES_DIR\n", "except NameError:\n", "    BASE_DIR = Path('.') if (Path('.')/'data').exists() else Path('..')\n", "    REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n", "    PLOTS_DIR = REPORTS_DIR / 'plots'\n", "    TABLES_DIR = REPORTS_DIR / 'tables'\n", "    PLOTS_DIR.mkdir(parents=True, exist_ok=True); TABLES_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# Carrega ranking (tuned -> baseline)\n", "rank_tuned = TABLES_DIR / 'algorithm_ranking_tuned.csv'\n", "rank_base  = TABLES_DIR / 'algorithm_ranking.csv'\n", "if rank_tuned.exists():\n", "    rank = pd.read_csv(rank_tuned)\n", "elif rank_base.exists():\n", "    rank = pd.read_csv(rank_base)\n", "else:\n", "    rank = pd.Data<PERSON>rame()\n", "\n", "def _best_model_name(df: pd.DataFrame) -> str:\n", "    if df.empty:\n", "        return 'RF'\n", "    df2 = df.copy()\n", "    if 'rmse_mean' in df2.columns:\n", "        df2['rmse_pos'] = df2['rmse_mean'].abs()\n", "        return str(df2.sort_values('rmse_pos', ascending=True).iloc[0]['model'])\n", "    if 'r2_mean' in df2.columns:\n", "        return str(df2.sort_values('r2_mean', ascending=False).iloc[0]['model'])\n", "    # fallback gen<PERSON><PERSON><PERSON>\n", "    return str(df2.iloc[0]['model'])\n", "\n", "winner = _best_model_name(rank)\n", "print('<PERSON><PERSON> (ranking):', winner)\n", "\n", "# Define função para montar pipeline de pré‑processamento + estimador\n", "def make_pipeline(model_name: str, num_cols, cat_cols):\n", "    ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=True)\n", "    pre = ColumnTransformer([\n", "        ('num', StandardScaler(with_mean=False), num_cols),\n", "        ('cat', ohe, cat_cols)\n", "    ], remainder='drop')\n", "    if model_name in ['LinReg','Linear','LinearRegression','LR']:\n", "        est = LinearRegression()\n", "    elif model_name in ['RF','RandomForest','RandomForestRegressor']:\n", "        est = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    elif model_name in ['SVR','SVM']:\n", "        est = SVR(kernel='rbf', C=5.0, gamma='scale', epsilon=0.1)\n", "    else:\n", "        est = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    return Pipeline([('pre', pre), ('model', est)])\n", "\n", "# Prepara dados (usa df do notebook; fallback lê CLEAN_DATA)\n", "try:\n", "    df_mod = df.copy()\n", "except NameError:\n", "    try:\n", "        df_mod = pd.read_csv(CLEAN_DATA, parse_dates=['data'])\n", "    except Exception:\n", "        BASE_DIR = Path('.') if (Path('.')/'data').exists() else Path('..')\n", "        df_mod = pd.read_csv(BASE_DIR/'data'/'clean'/'cleaned_featured.csv', parse_dates=['data'])\n", "\n", "# Define alvo e colunas num/cat\n", "target = 'valor' if 'valor' in df_mod.columns else None\n", "num_cols = [c for c in df_mod.columns if pd.api.types.is_numeric_dtype(df_mod[c]) and c != target]\n", "cat_candidates = ['UF','uf','Cidade','cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n", "cat_cols = [c for c in cat_candidates if c in df_mod.columns]\n", "\n", "# Limpeza básica e split X/y\n", "df_train = df_mod.copy()\n", "if target is None:\n", "    raise RuntimeError('Coluna alvo valor não encontrada.')\n", "df_train = df_train[num_cols + cat_cols + [target]].dropna()\n", "X = df_train[num_cols + cat_cols]\n", "y = pd.to_numeric(df_train[target], errors='coerce').fillna(0.0)\n", "\n", "# Monta pipeline e treina\n", "pipe = make_pipeline(winner, num_cols, cat_cols)\n", "pipe.fit(X, y)\n", "print('Pipeline treinado com sucesso. n amostras =', len(X))\n", "\n", "# Salva modelo final\n", "MODELS_DIR = Path('models') if Path('models').exists() else (Path('..')/'models')\n", "MODELS_DIR.mkdir(parents=True, exist_ok=True)\n", "out_path = MODELS_DIR / 'final_model.pkl'\n", "joblib.dump(pipe, out_path)\n", "print('<PERSON>o final salvo em:', out_path)\n", "\n", "# Exporta justificativa resumida\n", "summary = {'winner': winner}\n", "if not rank.empty:\n", "    summary['ranking_head'] = rank.head(5).to_dict(orient='list')\n", "(TABLES_DIR/'final_model_summary.json').write_text(json.dumps(summary, ensure_ascii=False, indent=2), encoding='utf-8')\n", "print('Resumo salvo em tables/final_model_summary.json')\n"]}, {"cell_type": "markdown", "id": "66f79428", "metadata": {}, "source": ["## 7. Explicabilidade do Modelo Final\n", "\n", "Aplicamos SHAP (quando disponível) ou importâncias de features do estimador para identificar os principais fatores que explicam as previsões. As figuras são exportadas para reports/2025-08-15/plots/."]}, {"cell_type": "markdown", "id": "7944f0d6", "metadata": {}, "source": ["### [auto-doc] Etapa 39\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7a41d7ce", "metadata": {}, "outputs": [], "source": ["#@title Geração de SHAP ou Importância de Features do modelo final\n", "import numpy as np, pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import joblib\n", "from sklearn.inspection import permutation_importance\n", "\n", "# Recarrega objetos úteis\n", "try:\n", "    REPORTS_DIR, PLOTS_DIR, TABLES_DIR\n", "except NameError:\n", "    BASE_DIR = Path('.') if (Path('.')/'data').exists() else Path('..')\n", "    REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n", "    PLOTS_DIR = REPORTS_DIR / 'plots'\n", "    TABLES_DIR = REPORTS_DIR / 'tables'\n", "    PLOTS_DIR.mkdir(parents=True, exist_ok=True); TABLES_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# Recarrega dataset e modelo\n", "try:\n", "    df_expl = df.copy()\n", "except NameError:\n", "    df_expl = pd.read_csv(CLEAN_DATA, parse_dates=['data'])\n", "\n", "model_path = (Path('models')/'final_model.pkl') if (Path('models')/'final_model.pkl').exists() else (Path('..')/'models'/'final_model.pkl')\n", "pipe = joblib.load(model_path)\n", "# Identifica colunas usadas\n", "target = 'valor'\n", "num_cols = [c for c in df_expl.columns if pd.api.types.is_numeric_dtype(df_expl[c]) and c != target]\n", "cat_candidates = ['UF','uf','Cidade','cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n", "cat_cols = [c for c in cat_candidates if c in df_expl.columns]\n", "dfx = df_expl[num_cols + cat_cols + [target]].dropna().copy()\n", "X_all = dfx[num_cols + cat_cols]\n", "y_all = pd.to_numeric(dfx[target], errors='coerce').fillna(0.0)\n", "\n", "# Amostra para explicabilidade (controle de custo)\n", "rng = np.random.RandomState(42)\n", "sample_n = min(800, len(X_all))\n", "idx = rng.choice(len(X_all), sample_n, replace=False)\n", "X_small = X_all.iloc[idx]\n", "y_small = y_all.iloc[idx]\n", "\n", "# Tenta SHAP com dados transformados\n", "feature_names = None\n", "try:\n", "    pre = pipe.named_steps['pre']\n", "    Xt = pre.transform(X_small)\n", "    # Tenta obter nomes das features pós‑transformação\n", "    try:\n", "        feature_names = pre.get_feature_names_out()\n", "    except Exception:\n", "        feature_names = None\n", "    model = pipe.named_steps['model']\n", "    import shap\n", "    explainer = None\n", "    # TreeExplainer para <PERSON>, caso contr<PERSON>rio Explainer gen<PERSON><PERSON>o\n", "    try:\n", "        from sklearn.ensemble import RandomForestRegressor\n", "        if isinstance(model, RandomForestRegressor):\n", "            explainer = shap.<PERSON>Explainer(model)\n", "    except Exception:\n", "        explainer = None\n", "    if explainer is None:\n", "        explainer = shap.Explainer(model, Xt)\n", "    n_obs = Xt.shape[0] if hasattr(Xt, 'shape') else len(X_small)\n", "    take = min(512, n_obs)\n", "    sv = explainer(Xt[:take])\n", "    shap.summary_plot(sv, Xt[:take], show=False, feature_names=feature_names)\n", "    plt.tight_layout(); plt.savefig(PLOTS_DIR/'shap_final_model.png', dpi=200, bbox_inches='tight'); plt.close()\n", "    print('SHAP gerado em plots/shap_final_model.png')\n", "except Exception as e:\n", "    print('SHAP indisponível, usando importâncias ->', e)\n", "    try:\n", "        model = pipe.named_steps['model']\n", "        if hasattr(model, 'feature_importances_'):\n", "            # Ajusta em todo X_small para sincronizar importâncias\n", "            pipe.fit(X_small, y_small)\n", "            model = pipe.named_steps['model']\n", "            # Recupera nomes pós‑transformação se possível\n", "            pre = pipe.named_steps['pre']\n", "            try:\n", "                feature_names = pre.get_feature_names_out()\n", "            except Exception:\n", "                feature_names = [f'feat_{i}' for i in range(len(model.feature_importances_))]\n", "            imps = pd.Series(model.feature_importances_, index=feature_names)\n", "            top = imps.sort_values(ascending=False).head(20)\n", "            plt.figure(figsize=(10,6)); top[::-1].plot(kind='barh'); plt.title('Importância de Features (Top 20)'); plt.tight_layout(); plt.savefig(PLOTS_DIR/'feature_importance_final_model.png', dpi=200); plt.close()\n", "            top.to_csv(TABLES_DIR/'feature_importance_final_model_top20.csv', index=True)\n", "            print('Importâncias exportadas (top 20).')\n", "        else:\n", "            # Permutation importance como último recurso\n", "            pipe.fit(X_small, y_small)\n", "            r = permutation_importance(pipe, X_small, y_small, n_repeats=5, random_state=42)\n", "            feats = np.array(X_small.columns)\n", "            imp = pd.Series(r.importances_mean, index=feats).sort_values(ascending=False).head(20)\n", "            plt.figure(figsize=(10,6)); imp[::-1].plot(kind='barh'); plt.title('Permutation Importance (Top 20)'); plt.tight_layout(); plt.savefig(PLOTS_DIR/'perm_importance_final_model.png', dpi=200); plt.close()\n", "            imp.to_csv(TABLES_DIR/'perm_importance_final_model_top20.csv', index=True)\n", "            print('Permutation importance exportada (top 20).')\n", "    except Exception as e2:\n", "        print('Falha geral em explicabilidade:', e2)\n", "\n", "# Comentário automático dos fatores mais importantes (Top 5)\n", "try:\n", "    path = TABLES_DIR/'feature_importance_final_model_top20.csv'\n", "    if path.exists():\n", "        s = pd.read_csv(path)\n", "        top5 = s.iloc[:5,0].tolist()\n", "        print('<PERSON>ores mais importantes (Top 5):', ', '.join(map(str, top5)))\n", "except Exception:\n", "    pass\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "89397de0", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "ed967e00", "metadata": {}, "source": ["## 8. Conclusões e Próximos Passos\n", "\n", "- Consolidamos a seleção e o treinamento do modelo final com base nas métricas de comparação.\n", "\n", "- Exportamos o artefato models/final_model.pkl e as visualizações de explicabilidade.\n", "\n", "- Próximos passos: (a) validação adicional por região/cluster; (b) monitoramento com baseline; (c) preparação de ambiente de inferência."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}