{"cells": [{"cell_type": "code", "execution_count": null, "id": "cd013b50", "metadata": {}, "outputs": [], "source": ["# [anti-id] helpers – escolher dimensão de negócio em vez de IDs\n", "SAFE_DIM_PRIORITY = ['UF','uf','Cidade','cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n", "def SAFE_DIM_OF(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in SAFE_DIM_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_PRIORITY[0]\n", "# Var global padrão – tenta inferir de df se existir, senão usa primeira opção\n", "try:\n", "    SAFE_DIM = SAFE_DIM_OF(df)\n", "except Exception:\n", "    SAFE_DIM = SAFE_DIM_PRIORITY[0]\n"]}, {"cell_type": "code", "execution_count": null, "id": "7e803d35", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "code", "execution_count": null, "id": "bec8924f", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "markdown", "id": "a12878f4", "metadata": {}, "source": ["# EDA – Geográfico\n", "\n", "Análises por UF/cidade, mapas e sumários regionais."]}, {"cell_type": "markdown", "id": "4f08e604", "metadata": {}, "source": ["### [auto-doc] Etapa 1\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "dfd7178e", "metadata": {}, "outputs": [], "source": ["# [auto-doc] estilo global\n", "import matplotlib as mpl, seaborn as sns\n", "import matplotlib.pyplot as plt\n", "sns.set_theme(style='whitegrid', context='notebook', palette='deep')\n", "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "0e605b71", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "22c3ead5", "metadata": {}, "source": ["### [auto-doc] Etapa 2\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "62859be4", "metadata": {}, "outputs": [], "source": ["import matplotlib as mpl, seaborn as sns\n", "import matplotlib.pyplot as plt\n", "sns.set_theme(style='whitegrid', context='notebook')\n", "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "9238ef22", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "753fce2b", "metadata": {}, "source": ["# Análise Territorial Abrangente – <PERSON>lli<PERSON><PERSON> (Vanessa)\n", "\n", "**Objetivo:** Comparar abordagens supervisionadas e não-supervisionadas para ranquear regiões por potencial de expansão.\n", "\n", "Este notebook demonstra ambas as metodologias, permitindo à equipe escolher a estratégia mais adequada conforme o contexto de negócio."]}, {"cell_type": "markdown", "id": "9e10bd46", "metadata": {}, "source": ["## 1. <PERSON><PERSON><PERSON><PERSON> Exploratória Geográfica"]}, {"cell_type": "markdown", "id": "aef658c4", "metadata": {}, "source": ["### [auto-doc] Etapa 3\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d174b8e0", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:23.172687Z", "iopub.status.busy": "2025-09-15T15:22:23.172687Z", "iopub.status.idle": "2025-09-15T15:22:26.866670Z", "shell.execute_reply": "2025-09-15T15:22:26.865686Z"}}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np, json\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.cluster import KMeans\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import KFold, cross_validate\n", "from sklearn.metrics import silhouette_score\n", "import warnings; warnings.filterwarnings('ignore')\n", "import joblib\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n", "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n", "REPORTS = BASE/'reports'/'2025-08-15'/'territorial_analysis'\n", "MODELS = BASE/'models'\n", "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n", "print('Carregando dados de:', DATA)\n"]}, {"cell_type": "markdown", "id": "182c93b6", "metadata": {}, "source": ["### [auto-doc] Etapa 4\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "markdown", "id": "b6e32257", "metadata": {}, "source": ["### [auto-doc] Etapa 5\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "markdown", "id": "0553ff31", "metadata": {}, "source": ["### 1.1 Mapa Exploratório de Vendas"]}, {"cell_type": "markdown", "id": "5364ef79", "metadata": {}, "source": ["### [auto-doc] Etapa 6\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "be8d0ca3", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:28.797974Z", "iopub.status.busy": "2025-09-15T15:22:28.797974Z", "iopub.status.idle": "2025-09-15T15:22:28.810695Z", "shell.execute_reply": "2025-09-15T15:22:28.810695Z"}}, "outputs": [], "source": ["# Mapa de vendas por região usando folium\n", "try:\n", "    import folium\n", "    center = [-14.2350, -51.9253]  # Centro do Brasil\n", "    m_exploratory = folium.Map(location=center, zoom_start=4)\n", "    if {'latitude','longitude'}.issubset(regional_data.columns):\n", "        for _, row in regional_data.iterrows():\n", "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n", "                receita = row.get('valor_sum', 0)\n", "                radius = max(5, min(20, receita/10000))  # Escala do círculo\n", "                folium.CircleMarker(\n", "                    [row['latitude'], row['longitude']],\n", "                    radius=radius,\n", "                    color='blue',\n", "                    fill=True,\n", "                    popup=f\"{row[GROUP_KEY]}: R$ {receita:,.0f}\"\n", "                ).add_to(m_exploratory)\n", "        m_exploratory.save(str(REPORTS/'mapa_exploratorio.html'))\n", "        print('Mapa exploratório salvo em:', REPORTS/'mapa_exploratorio.html')\n", "    else:\n", "        print('Coordenadas não disponíveis para mapeamento')\n", "except ImportError:\n", "    print('Folium não disponível - pulando mapeamento')\n"]}, {"cell_type": "markdown", "id": "68d88c94", "metadata": {}, "source": ["### 1.2 Preparação de Features para Modelagem"]}, {"cell_type": "markdown", "id": "3cf84227", "metadata": {}, "source": ["### [auto-doc] Etapa 7\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ec600d2c", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:28.814806Z", "iopub.status.busy": "2025-09-15T15:22:28.813823Z", "iopub.status.idle": "2025-09-15T15:22:28.868348Z", "shell.execute_reply": "2025-09-15T15:22:28.868348Z"}}, "outputs": [], "source": ["# Selecionar features numéricas para modelagem\n", "feature_cols = [c for c in regional_data.columns if c != GROUP_KEY and pd.api.types.is_numeric_dtype(regional_data[c])]\n", "# Remover coordenadas das features (usar apenas para visualização)\n", "feature_cols = [c for c in feature_cols if c not in ['latitude','longitude']]\n", "# Preparar matriz de features\n", "X_raw = regional_data[feature_cols].fillna(regional_data[feature_cols].median())\n", "# Definir target para abordagem supervisionada\n", "if 'valor_sum' in regional_data.columns:\n", "    y_raw = regional_data['valor_sum'].values\n", "    X_features = X_raw.drop(columns=['valor_sum'], errors='ignore')\n", "else:\n", "    # Fallback: usar primeira coluna numérica como proxy\n", "    y_raw = X_raw.iloc[:,0].values\n", "    X_features = X_raw.iloc[:,1:]\n", "print('Features para modelagem:', X_features.columns.tolist())\n", "print('Target (receita):', f'min={y_raw.min():.0f}, max={y_raw.max():.0f}, mean={y_raw.mean():.0f}')\n"]}, {"cell_type": "markdown", "id": "d100b7e3", "metadata": {}, "source": ["## 2. Abordagem Supervisionada - Modelo Preditivo"]}, {"cell_type": "markdown", "id": "417f8c69", "metadata": {}, "source": ["### [auto-doc] Etapa 8\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9ac0a95f", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:28.872261Z", "iopub.status.busy": "2025-09-15T15:22:28.871283Z", "iopub.status.idle": "2025-09-15T15:22:35.366005Z", "shell.execute_reply": "2025-09-15T15:22:35.365464Z"}}, "outputs": [], "source": ["# Normalizar features para modelagem\n", "scaler_sup = StandardScaler()\n", "X_scaled = scaler_sup.fit_transform(X_features)\n", "# Configurar modelo Random Forest\n", "rf_model = RandomForestRegressor(\n", "    n_estimators=400,\n", "    max_depth=10,\n", "    min_samples_split=5,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "# Validação cruzada 5-fold\n", "cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "scoring = {'rmse':'neg_root_mean_squared_error','mae':'neg_mean_absolute_error','r2':'r2'}\n", "cv_results = cross_validate(rf_model, X_scaled, y_raw, cv=cv, scoring=scoring, n_jobs=1)\n", "# Comp<PERSON>r mé<PERSON>\n", "metrics_sup = {}\n", "for metric, scores in cv_results.items():\n", "    if metric.startswith('test_'):\n", "        name = metric.replace('test_', '')\n", "        values = -scores if 'neg_' in metric else scores\n", "        metrics_sup[name] = {'mean': float(np.mean(values)), 'std': float(np.std(values))}\n", "print('Métricas Supervisionadas (CV 5-fold):')\n", "for name, stats in metrics_sup.items():\n", "    print(f'  {name}: {stats[\"mean\"]:.4f} ± {stats[\"std\"]:.4f}')\n"]}, {"cell_type": "markdown", "id": "54deec53", "metadata": {}, "source": ["### [auto-doc] Etapa 9\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1c4b9e86", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:35.369307Z", "iopub.status.busy": "2025-09-15T15:22:35.369307Z", "iopub.status.idle": "2025-09-15T15:22:35.994035Z", "shell.execute_reply": "2025-09-15T15:22:35.993508Z"}}, "outputs": [], "source": ["# Treinar modelo final e gerar scores 0-10\n", "rf_model.fit(X_scaled, y_raw)\n", "y_pred = rf_model.predict(X_scaled)\n", "# Normalizar para score 0-10\n", "score_scaler = MinMaxScaler(feature_range=(0, 10))\n", "scores_supervised = score_scaler.fit_transform(y_pred.reshape(-1, 1)).ravel()\n", "# Criar ranking supervisionado\n", "ranking_sup = regional_data[[GROUP_KEY]].copy()\n", "ranking_sup['receita_real'] = y_raw\n", "ranking_sup['receita_pred'] = y_pred\n", "ranking_sup['score_supervisionado'] = scores_supervised\n", "ranking_sup = ranking_sup.sort_values('score_supervisionado', ascending=False).reset_index(drop=True)\n", "ranking_sup['rank_supervisionado'] = range(1, len(ranking_sup) + 1)\n", "# Salvar ranking\n", "ranking_sup.to_csv(REPORTS/'ranking_supervisionado.csv', index=False)\n", "print('Top 10 Regiões - Abordagem Supervisionada:')\n", "print(ranking_sup[['rank_supervisionado', GROUP_KEY, 'score_supervisionado']].head(10))\n"]}, {"cell_type": "markdown", "id": "273c72c5", "metadata": {}, "source": ["## 3. Abordagem Não-Supervisionada - Segmentação Territorial"]}, {"cell_type": "markdown", "id": "014369bd", "metadata": {}, "source": ["### [auto-doc] Etapa 10\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f032db73", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:35.997947Z", "iopub.status.busy": "2025-09-15T15:22:35.996974Z", "iopub.status.idle": "2025-09-15T15:22:36.837043Z", "shell.execute_reply": "2025-09-15T15:22:36.836300Z"}}, "outputs": [], "source": ["# Normalizar features para clustering\n", "scaler_unsup = StandardScaler()\n", "X_scaled_unsup = scaler_unsup.fit_transform(X_features)\n", "# Método do cotovelo + Sil<PERSON>ette para seleção de K\n", "k_range = range(3, 9)\n", "inertias = []\n", "silhouette_scores = []\n", "for k in k_range:\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    labels = kmeans.fit_predict(X_scaled_unsup)\n", "    inertias.append(kmeans.inertia_)\n", "    sil_score = silhouette_score(X_scaled_unsup, labels)\n", "    silhouette_scores.append(sil_score)\n", "    print(f'K={k}: Inertia={kmeans.inertia_:.2f}, Silhouette={sil_score:.3f}')\n", "# Selecionar melhor K por silhouette\n", "best_k = k_range[np.argmax(silhouette_scores)]\n", "best_silhouette = max(silhouette_scores)\n", "print(f'\\nMelhor K selecionado: {best_k} (<PERSON><PERSON>houette: {best_silhouette:.3f})')\n"]}, {"cell_type": "markdown", "id": "686cbcef", "metadata": {}, "source": ["### [auto-doc] Etapa 11\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a8e879a9", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:36.840960Z", "iopub.status.busy": "2025-09-15T15:22:36.839980Z", "iopub.status.idle": "2025-09-15T15:22:36.957259Z", "shell.execute_reply": "2025-09-15T15:22:36.957259Z"}}, "outputs": [], "source": ["# Treinar modelo final K-Means\n", "kmeans_final = KMeans(n_clusters=best_k, random_state=42, n_init=10)\n", "cluster_labels = kmeans_final.fit_predict(X_scaled_unsup)\n", "# Analisar clusters\n", "cluster_analysis = regional_data.copy()\n", "cluster_analysis['cluster'] = cluster_labels\n", "# Estatísticas por cluster\n", "if 'valor_sum' in cluster_analysis.columns:\n", "    agg_dict = {'valor_sum': ['count', 'mean', 'sum']}\n", "else:\n", "    agg_dict = {GROUP_KEY: 'count'}\n", "cluster_stats = cluster_analysis.groupby('cluster').agg(agg_dict).round(2)\n", "cluster_stats.columns = ['_'.join(col).strip() for col in cluster_stats.columns]\n", "print('Estatísticas por Cluster:')\n", "print(cluster_stats)\n", "# Criar ranking por cluster (baseado na receita média do cluster)\n", "if 'valor_sum' in cluster_analysis.columns:\n", "    cluster_performance = cluster_analysis.groupby('cluster')['valor_sum'].mean().sort_values(ascending=False)\n", "    cluster_ranking = {cluster: rank+1 for rank, cluster in enumerate(cluster_performance.index)}\n", "else:\n", "    cluster_ranking = {i: i+1 for i in range(best_k)}\n", "cluster_analysis['cluster_rank'] = cluster_analysis['cluster'].map(cluster_ranking)\n", "cluster_analysis = cluster_analysis.sort_values(['cluster_rank', 'valor_sum'], ascending=[True, False])\n", "# Salvar resultados\n", "cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].to_csv(REPORTS/'clusters_nao_supervisionado.csv', index=False)\n", "print('\\nTop 10 Regiões por Cluster (ordenado por performance do cluster):')\n", "print(cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].head(10))\n"]}, {"cell_type": "markdown", "id": "4acedee6", "metadata": {}, "source": ["## 4. Comparação Estratégica Entre Abordagens"]}, {"cell_type": "markdown", "id": "3ae1d991", "metadata": {}, "source": ["### [auto-doc] Etapa 12\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "588505fc", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:36.960195Z", "iopub.status.busy": "2025-09-15T15:22:36.960195Z", "iopub.status.idle": "2025-09-15T15:22:37.030296Z", "shell.execute_reply": "2025-09-15T15:22:37.030296Z"}}, "outputs": [], "source": ["# Merge dos resultados para comparação\n", "comparison = ranking_sup[[GROUP_KEY, 'rank_supervisionado', 'score_supervisionado']].merge(\n", "    cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']], on=GROUP_KEY, how='inner'\n", ")\n", "# Análise de concordância\n", "comparison['rank_diff'] = abs(comparison['rank_supervisionado'] - comparison['cluster_rank'])\n", "comparison['concordancia'] = comparison['rank_diff'] <= 3  # Concordância se diferença <= 3 posições\n", "concordancia_rate = comparison['concordancia'].mean()\n", "print(f'Taxa de concordância entre métodos: {concordancia_rate:.2%}')\n", "# Top regiões por ambos os métodos\n", "top_both = comparison[\n", "    (comparison['rank_supervisionado'] <= 10) & (comparison['cluster_rank'] <= 3)\n", "].sort_values('rank_supervisionado')\n", "print('\\nRegiões priorizadas por AMBOS os métodos:')\n", "print(top_both[[GROUP_KEY, 'rank_supervisionado', 'cluster_rank', 'score_supervisionado']])\n", "# Salvar comparação\n", "comparison.to_csv(REPORTS/'comparacao_metodos.csv', index=False)\n"]}, {"cell_type": "markdown", "id": "01d1e921", "metadata": {}, "source": ["### [auto-doc] Etapa 13\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b8cb561c", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.033233Z", "iopub.status.busy": "2025-09-15T15:22:37.033233Z", "iopub.status.idle": "2025-09-15T15:22:37.041177Z", "shell.execute_reply": "2025-09-15T15:22:37.041177Z"}}, "outputs": [], "source": ["# An<PERSON>lise das diferenças metodológicas\n", "print('=== ANÁLISE COMPARATIVA ===')\n", "print('\\nABORDAGEM SUPERVISIONADA:')\n", "print('- Foco: Predição de receita baseada em padrões históricos')\n", "print('- Vantagem: Quantifica potencial financeiro diretamente')\n", "print('- Limitação: Dependente da qualidade dos dados históricos')\n", "print('- Uso recomendado: Expansão em mercados similares aos existentes')\n", "print('\\nABORDAGEM NÃO-SUPERVISIONADA:')\n", "print('- Foco: Identificação de padrões e segmentos territoriais')\n", "print('- Vantagem: Descobre grupos não óbvios, útil para estratégias diferenciadas')\n", "print('- Limitação: Não quantifica diretamente o potencial financeiro')\n", "print('- <PERSON><PERSON> recomendado: Exploração de novos mercados, estratégias por segmento')\n", "print('\\nCONCORDÂNCIA:')\n", "print(f'- {concordancia_rate:.1%} das regiões têm ranking similar entre métodos')\n", "print(f'- {len(top_both)} regiões são priorizadas por ambos os métodos')\n"]}, {"cell_type": "markdown", "id": "9b98cb94", "metadata": {}, "source": ["## 5. Dashboard Interativo e Visualizações"]}, {"cell_type": "markdown", "id": "a277846f", "metadata": {}, "source": ["### [auto-doc] Etapa 14\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "525afda8", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.044112Z", "iopub.status.busy": "2025-09-15T15:22:37.044112Z", "iopub.status.idle": "2025-09-15T15:22:37.056102Z", "shell.execute_reply": "2025-09-15T15:22:37.056102Z"}}, "outputs": [], "source": ["# Mapa interativo completo\n", "try:\n", "    import folium\n", "    from folium import plugins\n", "    # Mapa base\n", "    center = [-14.2350, -51.9253]\n", "    m_complete = folium.Map(location=center, zoom_start=4)\n", "    # Cores para clusters\n", "    cluster_colors = ['red', 'blue', 'green', 'purple', 'orange', 'darkred', 'lightred', 'beige']\n", "    if {'latitude','longitude'}.issubset(regional_data.columns):\n", "        # Merge dados completos\n", "        map_data = regional_data.merge(comparison, on=GROUP_KEY, how='left')\n", "        for _, row in map_data.iterrows():\n", "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n", "                # <PERSON><PERSON><PERSON> baseado no score supervisionado\n", "                radius = max(5, min(15, row.get('score_supervisionado', 5)))\n", "                # Cor baseada no cluster\n", "                cluster_idx = int(row.get('cluster', 0)) % len(cluster_colors)\n", "                color = cluster_colors[cluster_idx]\n", "                # Popup informativo\n", "                popup_text = f\"\"\"\n", "                <b>{row[GROUP_KEY]}</b><br>\n", "                Score Supervisionado: {row.get('score_supervisionado', 'N/A'):.1f}<br>\n", "                Cluster: {row.get('cluster', 'N/A')}<br>\n", "                Rank Supervisionado: {row.get('rank_supervisionado', 'N/A')}<br>\n", "                Rank Cluster: {row.get('cluster_rank', 'N/A')}\n", "                \"\"\"\n", "                folium.CircleMarker(\n", "                    [row['latitude'], row['longitude']],\n", "                    radius=radius,\n", "                    color=color,\n", "                    fill=True,\n", "                    popup=folium.Popup(popup_text, max_width=300)\n", "                ).add_to(m_complete)\n", "        # Adicionar legenda\n", "        legend_html = '<div style=\"position: fixed; top: 10px; right: 10px; z-index:1000; background-color:white; padding:10px; border:2px solid grey;\">'\n", "        legend_html += '<h4>Legenda</h4>'\n", "        legend_html += '<p><b><PERSON><PERSON><PERSON>:</b> Score Supervisionado</p>'\n", "        legend_html += '<p><b>Cor:</b> Cluster Não-Supervisionado</p>'\n", "        legend_html += '</div>'\n", "        m_complete.get_root().html.add_child(folium.Element(legend_html))\n", "        m_complete.save(str(REPORTS/'mapa_interativo_completo.html'))\n", "        print('Mapa interativo completo salvo em:', REPORTS/'mapa_interativo_completo.html')\n", "    else:\n", "        print('Coordenadas não disponíveis para mapeamento completo')\n", "except ImportError:\n", "    print('Folium não disponível - pulando mapeamento interativo')\n"]}, {"cell_type": "markdown", "id": "782a5b3a", "metadata": {}, "source": ["## 6. Recomendaç<PERSON><PERSON> Executivas"]}, {"cell_type": "markdown", "id": "8a0179b6", "metadata": {}, "source": ["### [auto-doc] Etapa 15\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e483f3d4", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.059036Z", "iopub.status.busy": "2025-09-15T15:22:37.059036Z", "iopub.status.idle": "2025-09-15T15:22:37.069648Z", "shell.execute_reply": "2025-09-15T15:22:37.069648Z"}}, "outputs": [], "source": ["# Síntese dos resultados\n", "print('=== SÍNTESE EXECUTIVA ===')\n", "print(f'\\nTotal de regiões analisadas: {len(regional_data)}')\n", "print(f'Clusters identificados: {best_k}')\n", "print(f'Taxa de concordância entre métodos: {concordancia_rate:.1%}')\n", "# Top 5 integrado (priorizando concordância)\n", "top5_integrated = comparison.copy()\n", "# Score integrado: média ponderada (70% supervisionado, 30% cluster)\n", "top5_integrated['score_integrado'] = (\n", "    0.7 * (11 - top5_integrated['rank_supervisionado']) / 10 +  # Normalizar rank\n", "    0.3 * (best_k + 1 - top5_integrated['cluster_rank']) / best_k\n", ")\n", "top5_integrated = top5_integrated.sort_values('score_integrado', ascending=False)\n", "print('\\n=== TOP 5 REGIÕES INTEGRADAS ===')\n", "for i, (_, row) in enumerate(top5_integrated.head(5).iterrows(), 1):\n", "    print(f'{i}. {row[GROUP_KEY]}')\n", "    print(f'   Score Supervisionado: {row[\"score_supervisionado\"]:.1f} (Rank: {row[\"rank_supervisionado\"]})')\n", "    print(f'   Cluster: {row[\"cluster\"]} (Rank: {row[\"cluster_rank\"]})')\n", "    print(f'   Score Integrado: {row[\"score_integrado\"]:.3f}')\n", "    print()\n"]}, {"cell_type": "markdown", "id": "b8ff04b2", "metadata": {}, "source": ["### [auto-doc] Etapa 16\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d601b18c", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.072705Z", "iopub.status.busy": "2025-09-15T15:22:37.072705Z", "iopub.status.idle": "2025-09-15T15:22:37.128620Z", "shell.execute_reply": "2025-09-15T15:22:37.128027Z"}}, "outputs": [], "source": ["# Recomendações específicas por método\n", "recommendations = []\n", "# Top 5 supervisionado\n", "for i, (_, row) in enumerate(ranking_sup.head(5).iterrows(), 1):\n", "    recommendations.append({\n", "        'rank': i,\n", "        'regiao': row[GROUP_KEY],\n", "        'metodo': 'Supervisionado',\n", "        'score': row['score_supervisionado'],\n", "        'justificativa': f'Alto potencial de receita predito (R$ {row[\"receita_pred\"]:,.0f})',\n", "        'acao_recomendada': 'Expansão prioritária - ROI esperado alto'\n", "    })\n", "# Top clusters\n", "cluster_recs = cluster_analysis.groupby('cluster').first().sort_values('cluster_rank')\n", "for i, (cluster, row) in enumerate(cluster_recs.head(3).iterrows(), 1):\n", "    recommendations.append({\n", "        'rank': i,\n", "        'regiao': f'Cluster {cluster} (ex: {row[GROUP_KEY]})',\n", "        'metodo': 'Não-Supervisionado',\n", "        'score': f'Cluster {cluster}',\n", "        'justificativa': f'Segmento territorial de alto potencial',\n", "        'acao_recomendada': f'Estratégia diferenciada para cluster {cluster}'\n", "    })\n", "# Salvar recomendações\n", "rec_df = pd.DataFrame(recommendations)\n", "rec_df.to_csv(REPORTS/'recomendacoes_executivas.csv', index=False)\n", "print('Recomendações executivas salvas em:', REPORTS/'recomendacoes_executivas.csv')\n"]}, {"cell_type": "markdown", "id": "fed738be", "metadata": {}, "source": ["### 6.1 Persistência dos Modelos"]}, {"cell_type": "markdown", "id": "f455eff8", "metadata": {}, "source": ["### [auto-doc] Etapa 17\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a71f0512", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.131559Z", "iopub.status.busy": "2025-09-15T15:22:37.130579Z", "iopub.status.idle": "2025-09-15T15:22:37.447620Z", "shell.execute_reply": "2025-09-15T15:22:37.447620Z"}}, "outputs": [], "source": ["# Salvar modelos treinados\n", "supervised_model = {\n", "    'model': rf_model,\n", "    'scaler': scaler_sup,\n", "    'score_scaler': score_scaler,\n", "    'features': X_features.columns.tolist(),\n", "    'metrics': metrics_sup,\n", "    'group_key': GROUP_KEY\n", "}\n", "unsupervised_model = {\n", "    'model': kmeans_final,\n", "    'scaler': scaler_unsup,\n", "    'features': X_features.columns.tolist(),\n", "    'best_k': best_k,\n", "    'silhouette_score': best_silhouette,\n", "    'group_key': GROUP_KEY\n", "}\n", "joblib.dump(supervised_model, MODELS/'territorial_supervised.pkl')\n", "joblib.dump(unsupervised_model, MODELS/'territorial_unsupervised.pkl')\n", "print('Modelos salvos em:')\n", "print('- Supervisionado:', MODELS/'territorial_supervised.pkl')\n", "print('- <PERSON><PERSON>-supervisionado:', MODELS/'territorial_unsupervised.pkl')\n"]}, {"cell_type": "markdown", "id": "9fce2fb5", "metadata": {}, "source": ["## 7. Resumo Final e Próximos Passos"]}, {"cell_type": "markdown", "id": "d182e0de", "metadata": {}, "source": ["### [auto-doc] Etapa 18\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "bfbc5a3d", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.451204Z", "iopub.status.busy": "2025-09-15T15:22:37.451204Z", "iopub.status.idle": "2025-09-15T15:22:37.461191Z", "shell.execute_reply": "2025-09-15T15:22:37.460156Z"}}, "outputs": [], "source": ["print('=== RESUMO FINAL ===')\n", "print('\\n✅ ENTREGÁVEIS GERADOS:')\n", "print('- Ranking supervisionado:', REPORTS/'ranking_supervisionado.csv')\n", "print('- Clusters não-supervisionados:', REPORTS/'clusters_nao_supervisionado.csv')\n", "print('- Comparação de métodos:', REPORTS/'comparacao_metodos.csv')\n", "print('- Mapa interativo:', REPORTS/'mapa_interativo_completo.html')\n", "print('- Recomendações executivas:', REPORTS/'recomendacoes_executivas.csv')\n", "print('- Modelos treinados em models/')\n", "print('\\n🎯 PRÓXIMOS PASSOS:')\n", "print('1. Validar recomendações com equipe comercial')\n", "print('2. Coletar dados externos (demografia, concorrência) para enriquecer análise')\n", "print('3. Implementar monitoramento de performance das regiões priorizadas')\n", "print('4. Desenvolver dashboard executivo para acompanhamento contínuo')\n", "print('\\n📊 CRITÉRIOS DE ESCOLHA DO MÉTODO:')\n", "print('- Use SUPERVISIONADO quando: foco em ROI, mercados similares aos existentes')\n", "print('- Use NÃO-SUPERVISIONADO quando: exploração de novos mercados, estratégias diferenciadas')\n", "print('- Use AMBOS quando: decisões críticas que requerem múltiplas perspectivas')\n"]}, {"cell_type": "markdown", "id": "806c6c61", "metadata": {}, "source": ["## Visualizações de Clusterização Territorial (não supervisionado) e Performance Supervisionada por Cluster\n", "\n", "Integra os resultados de segmentação (Notebook 05) com a modelagem supervisionada (Notebook 03) para:\n", "1) visualizar como o algoritmo não supervisionado particiona o território; e 2) avaliar o desempenho do melhor modelo por cluster.\n"]}, {"cell_type": "markdown", "id": "1828a109", "metadata": {}, "source": ["### [auto-doc] Etapa 19\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "96544113", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.464724Z", "iopub.status.busy": "2025-09-15T15:22:37.463750Z", "iopub.status.idle": "2025-09-15T15:22:37.488080Z", "shell.execute_reply": "2025-09-15T15:22:37.487095Z"}}, "outputs": [], "source": ["# Override make_model and feature list to safe version (no ColumnTransformer)\n", "import pandas as pd\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.svm import SVR\n", "from sklearn.linear_model import LinearRegression\n", "\n", "def _is_leak_feat(c: str, targets=('valor','revenue','sales','y')):\n", "    cl = c.lower()\n", "    pats = []\n", "    for t in targets:\n", "        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']\n", "    return any(p in cl for p in pats)\n", "\n", "# recompute num_cols cleanly from current df\n", "if 'num_cols' in globals() and isinstance(num_cols, list) and num_cols:\n", "    _cands = [c for c in num_cols if c in df.columns]\n", "else:\n", "    _cands = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "if 'target' in globals() and target is not None:\n", "    base_targets = [target, 'valor','revenue','sales','y']\n", "else:\n", "    base_targets = ['valor','revenue','sales','y']\n", "num_cols = [c for c in _cands if (c not in base_targets and not _is_leak_feat(c, base_targets))]\n", "\n", "\n", "def make_model(name:str):\n", "    if name in ['RF','GB','RandomForest']:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    elif name in ['SVM','SVR']:\n", "        base = SVR(kernel='rbf')\n", "    elif name in ['LR','Linear','LinearRegression']:\n", "        base = LinearRegression()\n", "    else:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])\n", "print('make_model overridden with safe estimator; features:', len(num_cols))\n"]}, {"cell_type": "markdown", "id": "651aa222", "metadata": {}, "source": ["### [auto-doc] Etapa 20\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ecc4e49", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:22:37.491008Z", "iopub.status.busy": "2025-09-15T15:22:37.491008Z", "iopub.status.idle": "2025-09-15T15:24:18.910462Z", "shell.execute_reply": "2025-09-15T15:24:18.910462Z"}}, "outputs": [], "source": ["# Two-stage territorial visualization with robust fallbacks\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.svm import SVR\n", "import warnings; warnings.filterwarnings('ignore')\n", "\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed'/'features_engineered_regional.csv').exists(): BASE = Path('..')\n", "DATA = BASE/'data'/'processed'/'features_engineered_regional.csv' if (BASE/'data'/'processed'/'features_engineered_regional.csv').exists() else BASE/'data'/'processed'/'features_engineered.csv'\n", "REPORTS = BASE/'reports'/'2025-08-15'\n", "PLOTS = REPORTS/'plots'\n", "TABLES = REPORTS/'tables'\n", "PLOTS.mkdir(parents=True, exist_ok=True); TABLES.mkdir(parents=True, exist_ok=True)\n", "\n", "# Load main data\n", "df = pd.read_csv(DATA, low_memory=False)\n", "print('Loaded data:', DATA, df.shape)\n", "\n", "# 1) Load clustering labels from notebook 05 if available\n", "clust_assign_path = TABLES/'cluster_assignments.csv'\n", "cluster_labels = None\n", "if clust_assign_path.exists():\n", "    try:\n", "        cl = pd.read_csv(clust_assign_path)\n", "        if 'index' in cl.columns:\n", "            cl = cl.set_index('index').sort_index()\n", "        if 'cluster' in cl.columns:\n", "            if len(cl) >= len(df):\n", "                cluster_labels = cl['cluster'].iloc[:len(df)].values\n", "            else:\n", "                cluster_labels = cl['cluster'].reindex(range(len(df))).values\n", "        print('Loaded clustering assignments from', clust_assign_path)\n", "    except Exception as e:\n", "        print('WARN: failed to load cluster assignments ->', e)\n", "\n", "# Fallback: build proxy clusters from territorial features if needed\n", "if cluster_labels is None:\n", "    try:\n", "        territorial_cols = []\n", "        if 'UF' in df.columns: territorial_cols.append('UF')\n", "        territorial_cols += [c for c in df.columns if 'REGIAO_CHILLI' in c]\n", "        X_terr = df[territorial_cols].copy() if territorial_cols else pd.DataFrame(index=df.index)\n", "        transformers=[]\n", "        if 'UF' in X_terr.columns:\n", "            transformers.append(('uf_ohe', OneHotEncoder(handle_unknown='ignore'), ['UF']))\n", "        fixed = [c for c in X_terr.columns if c!='UF']\n", "        if fixed:\n", "            transformers.append(('pass', 'passthrough', fixed))\n", "        if not transformers:\n", "            num_cols=[c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "            X_terr = df[num_cols[:8]].copy(); transformers=[('sc','passthrough', X_terr.columns.tolist())]\n", "        pre = ColumnTransformer(transformers)\n", "        X_enc = pre.fit_transform(X_terr)\n", "        from sklearn.cluster import KMeans\n", "        k = 5\n", "        km = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "        cluster_labels = km.fit_predict(X_enc)\n", "        print('Built proxy clusters (k=5) from territorial signals')\n", "    except Exception as e:\n", "        print('ERROR: unable to construct proxy clusters ->', e)\n", "        cluster_labels = np.zeros(len(df), dtype=int)\n", "\n", "# 1) Territorial clustering visualization\n", "try:\n", "    map_path = PLOTS/'territorial_clustering_map.png'\n", "    if 'UF' in df.columns:\n", "        order = sorted(df['UF'].dropna().unique())\n", "        uf_cat = pd.Categorical(df['UF'], categories=order, ordered=True)\n", "        plt.figure(figsize=(12,6))\n", "        y = np.random.RandomState(42).rand(len(df))\n", "        sns.scatterplot(x=uf_cat, y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)\n", "        sizes = pd.Series(cluster_labels).value_counts().sort_index()\n", "        handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]\n", "        plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')\n", "        plt.title('Segmentação Territorial por UF (cores=clusters)')\n", "        plt.xlabel('UF'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout();\n", "        plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()\n", "    else:\n", "        region_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]\n", "        if region_oh:\n", "            vals = pd.DataFrame(df[region_oh].values, columns=region_oh)\n", "            idx = np.argmax(vals.values, axis=1)\n", "            cats = [region_oh[i] for i in idx]\n", "            cat_series = pd.Series(cats, index=df.index, name='territory')\n", "            order = sorted(list(pd.unique(cat_series)))\n", "            y = np.random.RandomState(42).rand(len(df))\n", "            plt.figure(figsize=(12,6))\n", "            sns.scatterplot(x=pd.Categorical(cat_series, categories=order, ordered=True), y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)\n", "            sizes = pd.Series(cluster_labels).value_counts().sort_index()\n", "            handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]\n", "            plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')\n", "            plt.title('Segmentação Territorial por Região (REGIAO_CHILLI) — clusters')\n", "            plt.xlabel('<PERSON>i<PERSON>'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout(); plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()\n", "        else:\n", "            sizes = pd.Series(cluster_labels).value_counts().sort_index()\n", "            plt.figure(figsize=(8,4)); sns.barplot(x=sizes.index, y=sizes.values)\n", "            plt.title('<PERSON><PERSON><PERSON> dos clusters (fallback)'); plt.xlabel('Cluster'); plt.ylabel('n'); plt.tight_layout(); plt.savefig(map_path, dpi=180); plt.show()\n", "except Exception as e:\n", "    print('WARN: failed to build territorial clustering map ->', e)\n", "\n", "# 2) Supervised model performance by cluster\n", "rank_path = TABLES/'algorithm_ranking.csv'\n", "best_model_name = None\n", "if rank_path.exists():\n", "    try:\n", "        rank = pd.read_csv(rank_path)\n", "        if 'rmse_mean' in rank.columns:\n", "            best_model_name = rank.sort_values('rmse_mean').iloc[0]['model']\n", "        elif 'r2_mean' in rank.columns:\n", "            best_model_name = rank.sort_values('r2_mean', ascending=False).iloc[0]['model']\n", "        print('Best model from ranking:', best_model_name)\n", "    except Exception as e:\n", "        print('WARN: could not read ranking ->', e)\n", "if best_model_name is None:\n", "    best_model_name = 'RF'\n", "\n", "num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "candidates = [c for c in ['valor','sq_valor','y','sales','revenue'] if c in df.columns]\n", "target = candidates[0] if candidates else (num_cols[0] if num_cols else None)\n", "if target in num_cols:\n", "    num_cols = [c for c in num_cols if c!=target]\n", "\n", "from typing import List\n", "\n", "def make_model(name:str):\n", "    if name in ['RF','GB','RandomForest']:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    elif name in ['SVM','SVR']:\n", "        base = SVR(kernel='rbf')\n", "    elif name in ['LR','Linear','LinearRegression']:\n", "        base = LinearRegression()\n", "    else:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    pre = ColumnTransformer([\n", "        ('num', StandardScaler(with_mean=False), num_cols)\n", "    ], remainder='drop')\n", "    return Pipeline([('pre', pre), ('model', base)])\n", "\n", "metrics_rows = []\n", "all_preds = []\n", "if target is None or not num_cols:\n", "    print('ERROR: target or numeric features not available; skipping supervised by cluster')\n", "else:\n", "    uniq = sorted(pd.Series(cluster_labels).dropna().unique())\n", "    for c_id in uniq:\n", "        mask = (pd.Series(cluster_labels)==c_id).values\n", "        dfc = df.loc[mask]\n", "        if len(dfc) < 100:\n", "            print(f'Cluster {int(c_id)}: too few samples ({len(dfc)}), skipping metrics')\n", "            continue\n", "        X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "        Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)\n", "        pipe = make_model(str(best_model_name))\n", "        try:\n", "            pipe.fit(Xtr, ytr)\n", "            yp = pipe.predict(Xte)\n", "            r2 = float(r2_score(yte, yp))\n", "            rmse = float(np.sqrt(mean_squared_error(yte, yp)))\n", "            mae = float(mean_absolute_error(yte, yp))\n", "            metrics_rows.append({'cluster':int(c_id),'n':int(len(dfc)),'r2':r2,'rmse':rmse,'mae':mae})\n", "            all_preds.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))\n", "        except Exception as e:\n", "            print(f'Cluster {int(c_id)}: training failed ->', e)\n", "\n", "    if metrics_rows:\n", "        mdf = pd.DataFrame(metrics_rows).sort_values('cluster')\n", "        mdf.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)\n", "        fig, axes = plt.subplots(1,3, figsize=(14,4))\n", "        sns.barplot(data=mdf, x='cluster', y='r2', ax=axes[0]); axes[0].set_title('R² por Cluster');\n", "        sns.barplot(data=mdf, x='cluster', y='rmse', ax=axes[1]); axes[1].set_title('RMSE por Cluster');\n", "        sns.barplot(data=mdf, x='cluster', y='mae', ax=axes[2]); axes[2].set_title('MAE por Cluster');\n", "        plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()\n", "\n", "    if all_preds:\n", "        preds = pd.concat(all_preds, ignore_index=True)\n", "        preds.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)\n", "        plt.figure(figsize=(6,6));\n", "        sns.scatterplot(data=preds, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)\n", "        lim = (min(preds['y_true'].min(), preds['y_pred'].min()), max(preds['y_true'].max(), preds['y_pred'].max()))\n", "        plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()\n", "\n", "# Feature importances per cluster (if supported)\n", "try:\n", "    if str(best_model_name) in ['RF','GB','RandomForest'] and num_cols:\n", "        for c_id in sorted(pd.Series(cluster_labels).dropna().unique()):\n", "            mask = (pd.Series(cluster_labels)==c_id).values\n", "            dfc = df.loc[mask]\n", "            if len(dfc) < 200: continue\n", "            X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "            pipe = make_model('RF'); pipe.fit(X, y)\n", "            model = pipe.named_steps['model']\n", "            if hasattr(model, 'feature_importances_'):\n", "                imps = model.feature_importances_\n", "                top_idx = np.argsort(imps)[-10:][::-1]\n", "                out = pd.DataFrame({'feature':[num_cols[i] for i in top_idx], 'importance':[float(imps[i]) for i in top_idx]})\n", "                out['cluster']=int(c_id)\n", "                out.to_csv(TABLES/f'feature_importances_top10_cluster_{int(c_id)}.csv', index=False)\n", "except Exception as e:\n", "    print('Feature importance by cluster skipped ->', e)\n"]}, {"cell_type": "markdown", "id": "5949d3ad", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "844b6d6c", "metadata": {}, "source": ["### [auto-doc] Etapa 21\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d6dd4269", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:24:18.913397Z", "iopub.status.busy": "2025-09-15T15:24:18.913397Z", "iopub.status.idle": "2025-09-15T15:54:58.475826Z", "shell.execute_reply": "2025-09-15T15:54:58.474699Z"}}, "outputs": [], "source": ["# Anti-leakage & advanced visuals (learning curves, residuals, CIs) — SAFE ESTIMATOR VERSION\n", "import re, math\n", "from sklearn.model_selection import learning_curve, KFold, train_test_split\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.svm import SVR\n", "from sklearn.linear_model import LinearRegression\n", "import numpy as np, pandas as pd, matplotlib.pyplot as plt, seaborn as sns\n", "from pathlib import Path\n", "\n", "# Detect target-derived columns to exclude\n", "base_targets = ['valor','revenue','sales','y']\n", "if 'target' in globals() and target is not None:\n", "    base_targets = list(dict.fromkeys([target, *base_targets]))\n", "\n", "def _is_leak(col: str) -> bool:\n", "    c = col.lower()\n", "    pats = []\n", "    for t in base_targets:\n", "        t = str(t).lower()\n", "        pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']\n", "    return any(p in c for p in pats)\n", "\n", "# Build clean features list from current df\n", "if 'num_cols' in globals() and isinstance(num_cols, list) and num_cols:\n", "    candidate_cols = [c for c in num_cols if c in df.columns]\n", "else:\n", "    candidate_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "clean_num_cols = [c for c in candidate_cols if not _is_leak(c)]\n", "if 'target' in globals() and target in clean_num_cols:\n", "    clean_num_cols = [c for c in clean_num_cols if c != target]\n", "print('Clean feature columns:', len(clean_num_cols))\n", "\n", "# Safe estimator that does not depend on ColumnTransformer or named columns\n", "\n", "def mk_estimator(name: str):\n", "    if name in ['RF','GB','RandomForest']:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    elif name in ['SVM','SVR']:\n", "        base = SVR(kernel='rbf')\n", "    elif name in ['LR','Linear','LinearRegression']:\n", "        base = LinearRegression()\n", "    else:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])\n", "\n", "# Recompute metrics with bootstrap CIs\n", "metrics_rows2 = []\n", "all_preds2 = []\n", "clusters = sorted(pd.Series(cluster_labels).dropna().unique()) if 'cluster_labels' in globals() else []\n", "for c_id in clusters:\n", "    mask = (pd.Series(cluster_labels)==c_id).values\n", "    dfc = df.loc[mask]\n", "    if len(dfc) < 120:\n", "        continue\n", "    X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "    Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)\n", "    est = mk_estimator(str(best_model_name) if 'best_model_name' in globals() and best_model_name is not None else 'RF')\n", "    est.fit(Xtr, ytr)\n", "    yp = est.predict(Xte)\n", "    r2 = float(r2_score(yte, yp)); rmse = float(np.sqrt(mean_squared_error(yte, yp))); mae = float(mean_absolute_error(yte, yp))\n", "    # Bootstrap CIs\n", "    rng = np.random.RandomState(42)\n", "    R2s=[]; RMSEs=[]; MAEs=[]\n", "    n=len(yte)\n", "    for b in range(200):\n", "        idx = rng.choice(n, n, replace=True)\n", "        yt = np.array(yte)[idx]; yp_b = np.array(yp)[idx]\n", "        with np.errstate(all='ignore'):\n", "            R2s.append(r2_score(yt, yp_b))\n", "        RMSEs.append(float(np.sqrt(mean_squared_error(yt, yp_b))))\n", "        MAEs.append(float(mean_absolute_error(yt, yp_b)))\n", "    def ci(a):\n", "        lo, hi = np.percentile(a, [2.5, 97.5])\n", "        return float(lo), float(hi)\n", "    r2_lo, r2_hi = ci(R2s); rmse_lo, rmse_hi = ci(RMSEs); mae_lo, mae_hi = ci(MAEs)\n", "    row = {'cluster':int(c_id),'n':int(len(dfc)), 'r2':r2,'r2_lo':r2_lo,'r2_hi':r2_hi,\n", "           'rmse':rmse,'rmse_lo':rmse_lo,'rmse_hi':rmse_hi,\n", "           'mae':mae,'mae_lo':mae_lo,'mae_hi':mae_hi}\n", "    metrics_rows2.append(row)\n", "    all_preds2.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))\n", "\n", "REPORTS = (Path('.')/'reports'/'2025-08-15') if (Path('.')/'reports'/'2025-08-15').exists() else (Path('..')/'reports'/'2025-08-15')\n", "PLOTS = REPORTS/'plots'; TABLES = REPORTS/'tables'\n", "PLOTS.mkdir(parents=True, exist_ok=True); TABLES.mkdir(parents=True, exist_ok=True)\n", "\n", "if metrics_rows2:\n", "    mdf2 = pd.DataFrame(metrics_rows2).sort_values('cluster')\n", "    mdf2.to_csv(TABLES/'supervised_by_cluster_metrics_ci.csv', index=False)\n", "    # Barplots with error bars\n", "    fig, axes = plt.subplots(1,3, figsize=(15,4))\n", "    axes[0].bar(mdf2['cluster'], mdf2['r2'], yerr=[mdf2['r2']-mdf2['r2_lo'], mdf2['r2_hi']-mdf2['r2']], capsize=4)\n", "    axes[0].set_title('R² por Cluster (95% CI)'); axes[0].set_xlabel('Cluster'); axes[0].set_ylabel('R²')\n", "    axes[1].bar(mdf2['cluster'], mdf2['rmse'], yerr=[mdf2['rmse']-mdf2['rmse_lo'], mdf2['rmse_hi']-mdf2['rmse']], capsize=4)\n", "    axes[1].set_title('RMSE por Cluster (95% CI)'); axes[1].set_xlabel('Cluster')\n", "    axes[2].bar(mdf2['cluster'], mdf2['mae'], yerr=[mdf2['mae']-mdf2['mae_lo'], mdf2['mae_hi']-mdf2['mae']], capsize=4)\n", "    axes[2].set_title('MAE por Cluster (95% CI)'); axes[2].set_xlabel('Cluster')\n", "    plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()\n", "\n", "if all_preds2:\n", "    preds2 = pd.concat(all_preds2, ignore_index=True)\n", "    preds2.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)\n", "    plt.figure(figsize=(6,6));\n", "    sns.scatterplot(data=preds2, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)\n", "    lim = (min(preds2['y_true'].min(), preds2['y_pred'].min()), max(preds2['y_true'].max(), preds2['y_pred'].max()))\n", "    plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()\n", "\n", "# Learning curves per cluster\n", "try:\n", "    for c_id in clusters:\n", "        dfc = df.loc[(pd.Series(cluster_labels)==c_id).values]\n", "        if len(dfc) < 200: continue\n", "        X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "        est = mk_estimator(str(best_model_name) if 'best_model_name' in globals() and best_model_name is not None else 'RF')\n", "        cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "        train_sizes, train_scores, val_scores = learning_curve(est, X, y, cv=cv, scoring='r2', train_sizes=np.linspace(0.2, 1.0, 5))\n", "        tr_mean, tr_std = train_scores.mean(axis=1), train_scores.std(axis=1)\n", "        va_mean, va_std = val_scores.mean(axis=1), val_scores.std(axis=1)\n", "        plt.figure(figsize=(6,4))\n", "        plt.plot(train_sizes, tr_mean, 'o-', label='Treino'); plt.fill_between(train_sizes, tr_mean-tr_std, tr_mean+tr_std, alpha=0.2)\n", "        plt.plot(train_sizes, va_mean, 'o-', label='Validação'); plt.fill_between(train_sizes, va_mean-va_std, va_mean+va_std, alpha=0.2)\n", "        plt.title(f'Learning Curve — Cluster {int(c_id)}'); plt.xlabel('<PERSON>tras de treino'); plt.ylabel('R²'); plt.legend(); plt.tight_layout()\n", "        plt.savefig(PLOTS/f'learning_curve_cluster_{int(c_id)}.png', dpi=180); plt.show()\n", "except Exception as e:\n", "    print('Learning curves skipped ->', e)\n", "\n", "# Residual analysis per cluster\n", "try:\n", "    for c_id in clusters:\n", "        dfc = df.loc[(pd.Series(cluster_labels)==c_id).values]\n", "        if len(dfc) < 120: continue\n", "        X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "        est = mk_estimator(str(best_model_name) if 'best_model_name' in globals() and best_model_name is not None else 'RF')\n", "        Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)\n", "        est.fit(Xtr, ytr)\n", "        yp = est.predict(Xte)\n", "        res = yte.values - yp\n", "        plt.figure(figsize=(6,4)); sns.histplot(res, kde=True); plt.title(f'Residuals — Cluster {int(c_id)}'); plt.tight_layout(); plt.savefig(PLOTS/f'residuals_hist_cluster_{int(c_id)}.png', dpi=180); plt.show()\n", "        plt.figure(figsize=(6,4)); plt.scatter(yp, res, s=10, alpha=0.6); plt.axhline(0,color='k',lw=1); plt.title(f'Residuals vs Fitted — Cluster {int(c_id)}'); plt.xlabel('Previsto'); plt.ylabel('Resíduo'); plt.tight_layout(); plt.savefig(PLOTS/f'residuals_vs_fitted_cluster_{int(c_id)}.png', dpi=180); plt.show()\n", "except Exception as e:\n", "    print('Residuals plots skipped ->', e)\n"]}, {"cell_type": "markdown", "id": "e61d467e", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "0330b93c", "metadata": {}, "source": ["### [auto-doc] Etapa 22\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1ebd5ba0", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:54:58.481742Z", "iopub.status.busy": "2025-09-15T15:54:58.480587Z", "iopub.status.idle": "2025-09-15T15:56:44.088737Z", "shell.execute_reply": "2025-09-15T15:56:44.087749Z"}}, "outputs": [], "source": ["# Recompute supervised-by-cluster using safe estimator (avoid column name mismatches)\n", "import numpy as np, pandas as pd, matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import train_test_split, KFold, learning_curve\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.svm import SVR\n", "from sklearn.linear_model import LinearRegression\n", "\n", "# Build safe estimator without ColumnTransformer (X already numeric)\n", "def mk_estimator(name: str):\n", "    if name in ['RF','GB','RandomForest']:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    elif name in ['SVM','SVR']:\n", "        base = SVR(kernel='rbf')\n", "    elif name in ['LR','Linear','LinearRegression']:\n", "        base = LinearRegression()\n", "    else:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])\n", "\n", "# Clean features list (ensure intersection with df columns)\n", "if 'clean_num_cols' not in globals() or not clean_num_cols:\n", "    clean_num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "if target in clean_num_cols:\n", "    clean_num_cols = [c for c in clean_num_cols if c != target]\n", "clean_num_cols = [c for c in clean_num_cols if c in df.columns]\n", "\n", "metrics_rows3 = []\n", "all_preds3 = []\n", "clusters = sorted(pd.Series(cluster_labels).dropna().unique()) if 'cluster_labels' in globals() else []\n", "for c_id in clusters:\n", "    mask = (pd.Series(cluster_labels)==c_id).values\n", "    dfc = df.loc[mask]\n", "    if len(dfc) < 120: continue\n", "    X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "    Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)\n", "    est = mk_estimator(str(best_model_name))\n", "    est.fit(Xtr, ytr)\n", "    yp = est.predict(Xte)\n", "    r2 = float(r2_score(yte, yp)); rmse = float(np.sqrt(mean_squared_error(yte, yp))); mae = float(mean_absolute_error(yte, yp))\n", "    metrics_rows3.append({'cluster':int(c_id),'n':int(len(dfc)),'r2':r2,'rmse':rmse,'mae':mae})\n", "    all_preds3.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))\n", "\n", "if metrics_rows3:\n", "    mdf3 = pd.DataFrame(metrics_rows3).sort_values('cluster')\n", "    mdf3.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)\n", "    fig, axes = plt.subplots(1,3, figsize=(14,4))\n", "    sns.barplot(data=mdf3, x='cluster', y='r2', ax=axes[0]); axes[0].set_title('R² por Cluster');\n", "    sns.barplot(data=mdf3, x='cluster', y='rmse', ax=axes[1]); axes[1].set_title('RMSE por Cluster');\n", "    sns.barplot(data=mdf3, x='cluster', y='mae', ax=axes[2]); axes[2].set_title('MAE por Cluster');\n", "    plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()\n", "\n", "if all_preds3:\n", "    preds3 = pd.concat(all_preds3, ignore_index=True)\n", "    preds3.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)\n", "    plt.figure(figsize=(6,6));\n", "    sns.scatterplot(data=preds3, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)\n", "    lim = (min(preds3['y_true'].min(), preds3['y_pred'].min()), max(preds3['y_true'].max(), preds3['y_pred'].max()))\n", "    plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "e99837fb", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "code", "execution_count": null, "id": "6d830c02", "metadata": {}, "outputs": [], "source": ["# [auto-viz] Série temporal por UF (mensal + média móvel)\n", "df_ts = df.copy()\n", "df_ts['ano_mes'] = df_ts['data'].dt.to_period('M').dt.to_timestamp()\n", "kpi = df_ts.groupby(['uf','ano_mes'])['valor'].sum().reset_index()\n", "kpi['mm3'] = kpi.groupby('uf')['valor'].transform(lambda s: s.rolling(3, min_periods=1).mean())\n", "plt.figure(figsize=(10,5))\n", "sns.lineplot(data=kpi, x='ano_mes', y='valor', hue='uf', alpha=0.3)\n", "sns.lineplot(data=kpi, x='ano_mes', y='mm3', hue='uf')\n", "plt.title('<PERSON><PERSON><PERSON> mensal por UF com média móvel (3 meses)')\n", "plt.xlabel('Ano-Mês'); plt.ylabel('<PERSON><PERSON><PERSON>')\n", "plt.legend(title='UF', bbox_to_anchor=(1.05,1), loc='upper left')\n", "plt.tight_layout()\n", "plt.savefig('reports/2025-08-15/plots/serie_temporal_uf.png', dpi=150, bbox_inches='tight')\n", "plt.show()\n", "\n", "# [auto-viz] Mix de produtos por UF/tipo_pdv (top-5 categorias)\n", "df_mix = df.copy()\n", "cat_col = 'categoria' if 'categoria' in df_mix.columns else ('linha' if 'linha' in df_mix.columns else None)\n", "if cat_col is not None and 'tipo_pdv' in df_mix.columns:\n", "    agg = df_mix.groupby(['uf','tipo_pdv',cat_col])['valor'].sum().reset_index()\n", "    def topn(g, n=5):\n", "        g2 = g.sort_values('valor', ascending=False)\n", "        g2['rank'] = g2.groupby(['uf','tipo_pdv']).cumcount()+1\n", "        return g2[g2['rank']<=n]\n", "    top = topn(agg)\n", "    plt.figure(figsize=(10,6))\n", "    sns.barplot(data=top, x='uf', y='valor', hue=cat_col)\n", "    plt.title('Top-5 categorias por UF/tipo_pdv (receita)')\n", "    plt.xlabel('UF'); plt.ylabel('<PERSON><PERSON><PERSON>')\n", "    plt.legend(title='Categoria', bbox_to_anchor=(1.05,1), loc='upper left')\n", "    plt.tight_layout()\n", "    plt.savefig('reports/2025-08-15/plots/mix_produtos_top5.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# [auto-viz] Heatmap UF x mês (variação % vs média anual)\n", "df_hm = df.copy()\n", "df_hm['ano_mes'] = df_hm['data'].dt.to_period('M').dt.to_timestamp()\n", "tab = df_hm.pivot_table(index='uf', columns='ano_mes', values='valor', aggfunc='sum', fill_value=0)\n", "tab_pct = tab.apply(lambda r: (r - r.mean())/r.replace(0, r.mean())*100, axis=1)\n", "plt.figure(figsize=(12,6))\n", "sns.heatmap(tab_pct, cmap='coolwarm', center=0)\n", "plt.title('Variação % da receita por UF vs média anual (por mês)')\n", "plt.xlabel('<PERSON><PERSON><PERSON>'); plt.ylabel('UF')\n", "plt.tight_layout()\n", "plt.savefig('reports/2025-08-15/plots/heatmap_uf_mes_varpct.png', dpi=150, bbox_inches='tight')\n", "plt.show()\n", "\n", "# [auto-viz] Pareto 80/20 por cidade (receita)\n", "df_p = df.copy()\n", "if 'cidade' in df_p.columns:\n", "    city = df_p.groupby('cidade')['valor'].sum().sort_values(ascending=False).reset_index()\n", "    city['cum_pct'] = city['valor'].cumsum()/city['valor'].sum()*100\n", "    plt.figure(figsize=(10,5))\n", "    ax = sns.barplot(data=city.head(30), x='cidade', y='valor', color='tab:blue')\n", "    ax2 = ax.twinx()\n", "    ax2.plot(range(len(city.head(30))), city['cum_pct'].head(30), color='tab:red', marker='o')\n", "    ax2.set_ylabel('% acumulado')\n", "    plt.title('<PERSON><PERSON><PERSON> 80/20 – receita por cidade (top 30)')\n", "    ax.set_xlabel('Cidade'); ax.set_ylabel('<PERSON><PERSON><PERSON>')\n", "    plt.xticks(rotation=80)\n", "    plt.tight_layout()\n", "    plt.savefig('reports/2025-08-15/plots/pareto_cidade.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}